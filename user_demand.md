# 使用A2A协议，完成用户打卡记录查询的需求，各部分功能可以拆解为多个agent,大模型使用ollama本地部署的qwen3:8b模型
1. 创建一个企业微信应用实现消息交互，应用收到消息的同时需要获取用户信息，包括姓名、账号、邮箱。

2. 通过接收到的聊天消息拆解任务，比如用户消息说“我今天打卡了吗？”，则需要从中识别出“今天”这个关键词，然后调用工具查询今天的日期。如果用户消息说“我昨天打卡了吗？”，则需要识别出“昨天”这个关键词，调用工具查询昨天的日期（格式：%Y-%m-%d）。

3. 通过邮箱信息，到mysql数据库中user_info表中获取到对应的人员编码。pin字段为人员编码，email字段为邮箱地址。数据库连接信息：'mysql+pymysql://kqopr:Kq1234@localhost/kaoqin'

4. 通过日期、人员编码信息，调用接口http://**********:8001/api/v2/transaction/get/?key=ACCESS_KEY发送POST请求，以json格式发送请求参数starttime（格式：%Y-%m-%d %H:%M:%S）、endtime(格式：%Y-%m-%d %H:%M:%S)、pin，其中日期使用前面得到的日期，时间固定为00:00:01和23:59:59。参数示例如下：
```
{
    "starttime": "2025-07-23 00:00:01",
    "endtime": "2025-07-23 23:59:59",
    "pin":"000067"
}
```
接口返回示例如下：
```
{
    "ret": 0,
    "msg": "获取考勤记录 5 条。",
    "data": {
        "count": 5,
        "items": [
            {
                "id": 917092,
                "pin": "000067",
                "ename": "葛汝军",
                "deptnumber": "016",
                "deptname": "信息技术部",
                "checktime": "2025-07-23 08:12:07",
                "sn": "CJDB203760093",
                "alias": "A8十七楼北侧大门",
                "verify": 15,
                "stateno": "0",
                "state": ""
            },
            {
                "id": 917093,
                "pin": "000067",
                "ename": "葛汝军",
                "deptnumber": "016",
                "deptname": "信息技术部",
                "checktime": "2025-07-23 08:12:08",
                "sn": "CJDB203760093",
                "alias": "A8十七楼北侧大门",
                "verify": 15,
                "stateno": 0,
                "state": "上班签到"
            },
            {
                "id": 917912,
                "pin": "000067",
                "ename": "葛汝军",
                "deptnumber": "016",
                "deptname": "信息技术部",
                "checktime": "2025-07-23 17:30:41",
                "sn": "CJDB203760093",
                "alias": "A8十七楼北侧大门",
                "verify": 15,
                "stateno": 0,
                "state": "上班签到"
            },
            {
                "id": 917913,
                "pin": "000067",
                "ename": "葛汝军",
                "deptnumber": "016",
                "deptname": "信息技术部",
                "checktime": "2025-07-23 17:30:42",
                "sn": "CJDB203760093",
                "alias": "A8十七楼北侧大门",
                "verify": 15,
                "stateno": "0",
                "state": ""
            },
            {
                "id": 917914,
                "pin": "000067",
                "ename": "葛汝军",
                "deptnumber": "016",
                "deptname": "信息技术部",
                "checktime": "2025-07-23 17:30:43",
                "sn": "CJDB203760093",
                "alias": "A8十七楼北侧大门",
                "verify": 15,
                "stateno": "0",
                "state": ""
            }
        ]
    }
}
```
5. 用大模型解析查询到的打卡记录，找到时间最早和最晚的两条数据，其中最早的一条为上班记录，最晚的一条为下班记录。结合用户问题，通过企业微信回复给用户。比如用户消息说“我今天打卡了吗？”，回复格式为：你今天上班打卡时间是%H:%M:%S，下班打卡时间是%H:%M:%S。比如用户消息说“我昨天打卡了吗？”，回复格式为：你昨天上班打卡时间是%H:%M:%S，下班打卡时间是%H:%M:%S。
## 说明：以上节点划分只是粗略划分，在实际设计流程时可以优化拆解为最优流程。
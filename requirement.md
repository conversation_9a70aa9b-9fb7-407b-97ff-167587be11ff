# 企业微信考勤查询系统需求文档

## 1. 项目概述

### 1.1 项目背景
基于A2A协议构建企业微信考勤查询系统，通过多Agent架构实现用户自助查询打卡记录功能。系统使用本地部署的Ollama Qwen3:8b模型提供AI能力。

### 1.2 项目目标
- 提供便捷的企业微信考勤查询服务
- 实现自然语言交互的打卡记录查询
- 构建可扩展的多Agent架构
- 确保数据安全和用户隐私

## 2. 功能需求

### 2.1 核心功能
1. **企业微信消息接收与用户身份识别**
2. **自然语言意图识别与日期解析**
3. **用户信息查询与权限验证**
4. **考勤数据获取与处理**
5. **智能回复生成与消息推送**

### 2.2 详细功能描述

#### 2.2.1 企业微信集成模块
- **功能**: 创建企业微信应用，实现消息交互
- **输入**: 用户发送的文本消息
- **输出**: 用户信息（姓名、账号、邮箱）+ 消息内容
- **技术要求**: 企业微信API集成，Webhook接收

#### 2.2.2 意图识别与日期解析模块
- **功能**: 解析用户查询意图，提取时间关键词
- **输入**: 用户消息文本
- **处理逻辑**:
  - 识别"今天"关键词 → 获取当前日期
  - 识别"昨天"关键词 → 获取昨天日期
  - 日期格式: %Y-%m-%d
- **输出**: 标准化日期字符串

#### 2.2.3 用户信息查询模块
- **功能**: 通过邮箱获取员工编码
- **数据源**: MySQL数据库 kaoqin_users 表
- **查询字段**: pin(人员编码), email(邮箱地址)
- **连接信息**: mysql+pymysql://kqopr:Kq1234@localhost/kaoqin

#### 2.2.4 考勤数据获取模块
- **功能**: 调用考勤接口获取打卡记录
- **接口地址**: http://**********:8001/api/v2/transaction/get/?key=4107gu4z8qrmdwd6d37vinfyhx3stz7kybuegscjo-a_
- **请求方式**: POST
- **请求参数**:
  ```json
  {
    "starttime": "YYYY-MM-DD 00:00:01",
    "endtime": "YYYY-MM-DD 23:59:59", 
    "pin": "员工编码"
  }
  ```

#### 2.2.5 数据分析与回复生成模块
- **功能**: 使用AI模型分析打卡数据，生成回复
- **处理逻辑**:
  - 找到时间最早的记录作为上班时间
  - 找到时间最晚的记录作为下班时间
  - 根据查询日期生成对应回复
- **回复格式**:
  - 今天查询: "你今天上班打卡时间是HH:MM:SS，下班打卡时间是HH:MM:SS"
  - 昨天查询: "你昨天上班打卡时间是HH:MM:SS，下班打卡时间是HH:MM:SS"

## 3. 技术架构

### 3.1 系统架构
- **协议**: A2A (Agent-to-Agent)
- **AI模型**: Ollama本地部署 Qwen3:8b
- **数据库**: MySQL
- **集成平台**: 企业微信

### 3.2 Agent架构设计
1. **消息接收Agent**: 处理企业微信消息
2. **意图识别Agent**: NLP处理和日期解析
3. **数据查询Agent**: 数据库和API调用
4. **回复生成Agent**: AI分析和消息生成
5. **消息推送Agent**: 企业微信回复

## 4. 业务流程图

```mermaid
graph TD
    A[用户发送消息] --> B[企业微信应用接收]
    B --> C[获取用户信息<br/>姓名/账号/邮箱]
    C --> D[意图识别Agent<br/>解析时间关键词]
    D --> E{识别结果}
    E -->|今天| F[获取当前日期]
    E -->|昨天| G[获取昨天日期]
    F --> H[数据查询Agent]
    G --> H
    H --> I[通过邮箱查询<br/>用户编码pin]
    I --> J[调用考勤接口<br/>获取打卡记录]
    J --> K{是否有数据}
    K -->|无数据| L[生成无记录回复]
    K -->|有数据| M[AI分析Agent<br/>提取上下班时间]
    M --> N[生成格式化回复]
    L --> O[企业微信推送回复]
    N --> O
    O --> P[流程结束]
```

## 5. 数据流设计

### 5.1 输入数据
- 用户消息文本
- 用户身份信息（企业微信提供）

### 5.2 中间数据
- 解析后的日期字符串
- 用户邮箱对应的pin编码
- 考勤接口返回的原始数据

### 5.3 输出数据
- 格式化的考勤查询结果
- 企业微信回复消息

## 6. 接口规范

### 6.1 考勤查询接口
- **URL**: http://**********:8001/api/v2/transaction/get/?key=4107gu4z8qrmdwd6d37vinfyhx3stz7kybuegscjo-a_
- **Method**: POST
- **Content-Type**: application/json
- **Request Body**:
  ```json
  {
    "starttime": "2025-07-23 00:00:01",
    "endtime": "2025-07-23 23:59:59", 
    "pin": "000067"
  }
  ```
- **Response Format**:
  ```json
  {
    "ret": 0,
    "msg": "获取考勤记录 N 条",
    "data": {
      "count": 5,
      "items": [...]
    }
  }
  ```

### 6.2 数据库查询
- **表名**: kaoqin_users
- **查询字段**: pin, email
- **查询条件**: WHERE email = ?

## 7. 非功能性需求

### 7.1 性能要求
- 响应时间: < 3秒
- 并发用户: 支持100+用户同时查询
- 可用性: 99.5%

### 7.2 安全要求
- 数据传输加密
- 用户身份验证
- 敏感信息脱敏

### 7.3 可扩展性
- 支持更多查询类型（周/月统计）
- 支持多种消息平台集成
- Agent功能模块化设计

## 8. 风险评估

### 8.1 技术风险
- 企业微信API限制
- 考勤接口稳定性
- AI模型准确性

### 8.2 业务风险
- 用户隐私保护
- 数据准确性要求
- 系统依赖性

## 9. 开发计划

### 9.1 开发阶段
1. **Phase 1**: 企业微信集成和基础框架
2. **Phase 2**: 核心Agent开发
3. **Phase 3**: AI模型集成和优化
4. **Phase 4**: 测试和部署

### 9.2 里程碑
- Week 1: 企业微信应用创建完成
- Week 2: 基础Agent架构搭建
- Week 3: 核心功能开发完成
- Week 4: 系统测试和优化

## 10. 验收标准

### 10.1 功能验收
- [ ] 企业微信消息正常接收和回复
- [ ] 日期关键词识别准确率 > 95%
- [ ] 考勤数据查询成功率 > 98%
- [ ] 回复格式符合需求规范

### 10.2 性能验收
- [ ] 平均响应时间 < 3秒
- [ ] 系统稳定运行24小时无故障
- [ ] 支持并发查询测试
# 企业微信考勤查询系统开发计划

## 1. 项目整体结构设计

### 1.1 项目架构
```
kaoqin-system/
├── agents/                 # Agent模块
│   ├── message_receiver/   # 消息接收Agent
│   ├── intent_parser/      # 意图识别Agent
│   ├── data_query/         # 数据查询Agent
│   ├── reply_generator/    # 回复生成Agent
│   └── message_sender/     # 消息推送Agent
├── core/                   # 核心模块
│   ├── config/            # 配置管理
│   ├── database/          # 数据库连接
│   ├── models/            # 数据模型
│   └── utils/             # 工具函数
├── api/                   # API接口
│   ├── wechat/           # 企业微信API
│   └── attendance/       # 考勤API
├── tests/                # 测试模块
├── docs/                 # 文档
└── docker/               # 容器化配置
```

### 1.2 技术栈选择
- **后端框架**: FastAPI
- **Agent框架**: LangChain + A2A协议
- **AI模型**: Ollama Qwen3:8b
- **数据库**: MySQL + SQLAlchemy
- **消息队列**: Redis
- **容器化**: Docker + Docker Compose

## 2. 核心功能开发计划

### 2.1 功能一：企业微信消息接收与用户身份识别

#### 2.1.1 开发设计
- **Agent名称**: MessageReceiverAgent
- **职责**: 接收企业微信消息，提取用户身份信息
- **输入**: 企业微信Webhook消息
- **输出**: 标准化用户信息和消息内容
- **技术实现**: FastAPI + 企业微信SDK

#### 2.1.2 任务清单
- [ ] **Task 1.1**: 企业微信应用创建和配置
  - 创建企业微信应用
  - 配置应用权限和回调URL
  - 获取应用凭证(corp_id, agent_id, secret)
  
- [ ] **Task 1.2**: Webhook接收服务开发
  - 实现FastAPI Webhook端点
  - 消息签名验证
  - 消息格式解析
  
- [ ] **Task 1.3**: 用户身份识别模块
  - 用户ID到用户信息的映射
  - 用户权限验证
  - 异常用户处理
  
- [ ] **Task 1.4**: MessageReceiverAgent实现
  - Agent基础框架搭建
  - 消息接收逻辑
  - 数据标准化输出

#### 2.1.3 验收标准
- [ ] 能够正常接收企业微信消息
- [ ] 正确提取用户姓名、账号、邮箱
- [ ] 消息格式验证通过率100%
- [ ] 响应时间 < 500ms

#### 2.1.4 测试方案
- 单元测试：消息解析功能
- 集成测试：企业微信消息模拟
- 性能测试：并发消息处理

---

### 2.2 功能二：自然语言意图识别与日期解析

#### 2.2.1 开发设计
- **Agent名称**: IntentParserAgent
- **职责**: 解析用户查询意图，提取时间信息
- **输入**: 用户消息文本
- **输出**: 结构化查询参数(日期、查询类型)
- **技术实现**: Ollama Qwen3:8b + 正则表达式

#### 2.2.2 任务清单
- [x] **Task 2.1**: Ollama环境搭建
  - Ollama服务部署
  - Qwen3:8b模型下载和配置
  - API接口测试
  
- [ ] **Task 2.2**: 意图识别模型设计
  - 设计Prompt模板
  - 关键词识别规则
  - 日期解析逻辑
  
- [ ] **Task 2.3**: 日期处理工具开发
  - "今天"、"昨天"关键词识别
  - 日期格式标准化
  - 边界情况处理
  
- [ ] **Task 2.4**: IntentParserAgent实现
  - Agent框架集成
  - 意图识别流程
  - 结果验证和纠错

#### 2.2.3 验收标准
- [ ] 关键词识别准确率 > 95%
- [ ] 日期解析正确率 100%
- [ ] 支持"今天"、"昨天"查询
- [ ] 处理时间 < 1秒

#### 2.2.4 测试方案
- 单元测试：日期解析函数
- 功能测试：各种查询语句
- 准确性测试：100个测试用例

---

### 2.3 功能三：用户信息查询与权限验证

#### 2.3.1 开发设计
- **Agent名称**: DataQueryAgent
- **职责**: 查询用户信息，验证查询权限
- **输入**: 用户邮箱
- **输出**: 用户pin编码和权限状态
- **技术实现**: SQLAlchemy + MySQL

#### 2.3.2 任务清单
- [ ] **Task 3.1**: 数据库连接配置
  - MySQL连接池配置
  - SQLAlchemy ORM设置
  - 连接安全和加密
  
- [ ] **Task 3.2**: 数据模型定义
  - UserInfo模型定义
  - 数据库表结构验证
  - 索引优化设计
  
- [ ] **Task 3.3**: 用户查询服务
  - 邮箱到pin编码映射
  - 查询结果缓存
  - 异常处理机制
  
- [ ] **Task 3.4**: 权限验证模块
  - 用户权限检查
  - 查询频率限制
  - 安全日志记录

#### 2.3.3 验收标准
- [ ] 数据库查询成功率 > 99%
- [ ] 查询响应时间 < 200ms
- [ ] 支持并发查询
- [ ] 权限验证准确性100%

#### 2.3.4 测试方案
- 单元测试：数据库查询函数
- 性能测试：并发查询压力测试
- 安全测试：SQL注入防护

---

### 2.4 功能四：考勤数据获取与处理

#### 2.4.1 开发设计
- **Agent名称**: AttendanceQueryAgent
- **职责**: 调用考勤接口，获取打卡记录
- **输入**: 日期范围和用户pin
- **输出**: 原始考勤数据
- **技术实现**: HTTP Client + 数据处理

#### 2.4.2 任务清单
- [ ] **Task 4.1**: 考勤接口集成
  - HTTP客户端配置
  - 接口认证和安全
  - 请求参数标准化
  
- [ ] **Task 4.2**: 数据获取服务
  - 接口调用封装
  - 重试机制实现
  - 错误处理和日志
  
- [ ] **Task 4.3**: 数据预处理
  - 响应数据验证
  - 数据格式转换
  - 异常数据过滤
  
- [ ] **Task 4.4**: 缓存和优化
  - 查询结果缓存
  - 接口调用优化
  - 性能监控

#### 2.4.3 验收标准
- [ ] 接口调用成功率 > 98%
- [ ] 数据获取时间 < 2秒
- [ ] 数据格式正确性100%
- [ ] 支持重试和容错

#### 2.4.4 测试方案
- 单元测试：接口调用函数
- 集成测试：端到端数据获取
- 稳定性测试：长时间运行测试

---

### 2.5 功能五：智能回复生成与消息推送

#### 2.5.1 开发设计
- **Agent名称**: ReplyGeneratorAgent + MessageSenderAgent
- **职责**: 分析考勤数据，生成回复并推送
- **输入**: 考勤数据和查询上下文
- **输出**: 格式化回复消息
- **技术实现**: AI分析 + 企业微信API

#### 2.5.2 任务清单
- [ ] **Task 5.1**: 数据分析模块
  - 打卡时间提取算法
  - 上下班时间识别
  - 异常情况处理
  
- [ ] **Task 5.2**: 回复生成服务
  - 回复模板设计
  - 动态内容填充
  - 多语言支持准备
  
- [ ] **Task 5.3**: 消息推送模块
  - 企业微信发送API
  - 消息格式化
  - 发送状态跟踪
  
- [ ] **Task 5.4**: Agent协调机制
  - Agent间通信协议
  - 数据流管理
  - 错误恢复机制

#### 2.5.3 验收标准
- [ ] 时间提取准确率100%
- [ ] 回复格式符合规范
- [ ] 消息推送成功率 > 99%
- [ ] 端到端响应时间 < 3秒

#### 2.5.4 测试方案
- 单元测试：时间提取算法
- 功能测试：回复格式验证
- 端到端测试：完整流程测试

## 3. 开发时间线

### 3.1 Sprint 1 (Week 1): 基础设施和功能一
- Day 1-2: 项目结构搭建，开发环境配置
- Day 3-4: 企业微信应用创建和配置
- Day 5-7: MessageReceiverAgent开发和测试

### 3.2 Sprint 2 (Week 2): 功能二和三
- Day 1-3: Ollama环境搭建，IntentParserAgent开发
- Day 4-5: 数据库配置，DataQueryAgent开发
- Day 6-7: 功能二、三集成测试

### 3.3 Sprint 3 (Week 3): 功能四和五
- Day 1-3: AttendanceQueryAgent开发
- Day 4-5: ReplyGeneratorAgent和MessageSenderAgent开发
- Day 6-7: 功能四、五集成测试

### 3.4 Sprint 4 (Week 4): 系统集成和优化
- Day 1-3: 端到端集成测试
- Day 4-5: 性能优化和安全加固
- Day 6-7: 部署和验收测试

## 4. 风险控制

### 4.1 技术风险
- **风险**: Ollama模型性能不稳定
- **缓解**: 准备备用NLP方案，增加规则引擎
- **风险**: 企业微信API限制
- **缓解**: 实现请求频率控制，准备降级方案

### 4.2 进度风险
- **风险**: 开发进度延迟
- **缓解**: 每个功能独立开发，可并行进行
- **风险**: 集成复杂度高
- **缓解**: 早期进行接口定义，模拟数据测试

## 5. 质量保证

### 5.1 代码质量
- 代码覆盖率 > 80%
- 静态代码分析
- 代码审查机制

### 5.2 测试策略
- 单元测试：每个模块独立测试
- 集成测试：Agent间协作测试
- 端到端测试：完整业务流程测试
- 性能测试：并发和压力测试

### 5.3 部署策略
- Docker容器化部署
- 配置管理和环境隔离
- 监控和日志系统
- 自动化部署流水线
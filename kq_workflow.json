{"name": "企业微信考勤查询", "nodes": [{"parameters": {}, "id": "5f23a645-8939-4f7a-8598-95b9e5b78398", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"path": "webhook", "responseMode": "onReceived", "options": {}}, "id": "b4f1b9b7-4a78-4275-a36a-12a712d126a4", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [460, 300], "webhookId": "e8c6b7c9-9b4c-4b8a-9b0a-7e8f8d9f8e7d"}, {"parameters": {"jsCode": "const message = $input.item.json.body.content;\nconst email = $input.item.json.body.email;\nconst today = luxon.DateTime.now();\nlet targetDate;\nlet dayWord;\n\nif (message.includes('昨天')) {\n  targetDate = today.minus({ days: 1 });\n  dayWord = '昨天';\n} else {\n  targetDate = today;\n  dayWord = '今天';\n}\n\nreturn {\n  date: targetDate.toFormat('yyyy-MM-dd'),\n  email: email,\n  question: message,\n  dayWord: dayWord\n};"}, "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef", "name": "1. 解析用户意图", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [680, 300], "notes": "从用户消息中解析查询的日期（今天/昨天）和关键词"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT pin FROM user_info WHERE email = '{{ $json.email }}'"}, "id": "f9e8d7c6-b5a4-3210-fedc-ba9876543210", "name": "2. 查询人员编码", "type": "n8n-nodes-base.mySql", "typeVersion": 1, "position": [900, 300], "credentials": {"mySql": {"id": "1", "name": "Kaoqin DB"}}, "notes": "根据企微邮箱到数据库查询对应的人员编码（pin）"}, {"parameters": {"url": "http://10.17.16.9:8001/api/v2/transaction/get/?key=ACCESS_KEY", "options": {}, "bodyParametersJson": "{\n    \"starttime\": \"{{ $('1. 解析用户意图').item.json.date }} 00:00:01\",\n    \"endtime\": \"{{ $('1. 解析用户意图').item.json.date }} 23:59:59\",\n    \"pin\":\"{{ $('2. 查询人员编码').item.json.pin }}\"\n}", "jsonParameters": true}, "id": "c1b2a3d4-e5f6-7890-fedc-ba9876543210", "name": "3. 查询打卡记录", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1120, 300], "notes": "调用考勤接口，查询指定日期和人员的打卡流水记录"}, {"parameters": {"jsCode": "const records = $input.item.json.data.items;\nconst dayWord = $('1. 解析用户意图').item.json.dayWord;\n\nif (!records || records.length === 0) {\n  return {\n    json: {\n      response: `您${dayWord}没有查询到打卡记录。`\n    }\n  };\n}\n\n// 按时间排序找到最早和最晚的记录\nrecords.sort((a, b) => new Date(a.checktime) - new Date(b.checktime));\n\nconst startTime = records[0].checktime.split(' ')[1];\nconst endTime = records[records.length - 1].checktime.split(' ')[1];\n\nconst response = `您好，您${dayWord}的打卡记录：上班 ${startTime}，下班 ${endTime}。`;\n\nreturn {\n  json: {\n    response: response\n  }\n};"}, "id": "d4e5f6a1-b2c3-4d5e-8901-234567890abc", "name": "4. 提取打卡时间", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [1340, 300], "notes": "从返回的考勤数据中，找到最早（上班）和最晚（下班）的打卡时间，并组合成回复消息。\n\n注：根据需求描述中的“可以优化拆解为最优流程”，此处使用代码节点进行数据处理，比使用大模型更高效、稳定和节省成本。"}, {"parameters": {"responseCode": "200", "responseData": "{\"reply\":\"{{$json.response}}\"}", "options": {}}, "id": "e6f7a1b2-c3d4-5e6f-8901-234567890def", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 300]}], "connections": {"Start": {"main": [[{"node": "Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "1. 解析用户意图", "type": "main", "index": 0}]]}, "1. 解析用户意图": {"main": [[{"node": "2. 查询人员编码", "type": "main", "index": 0}]]}, "2. 查询人员编码": {"main": [[{"node": "3. 查询打卡记录", "type": "main", "index": 0}]]}, "3. 查询打卡记录": {"main": [[{"node": "4. 提取打卡时间", "type": "main", "index": 0}]]}, "4. 提取打卡时间": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "credentials": {"mySql": {"1": {"id": "1", "name": "Kaoqin DB", "data": {"host": "localhost", "database": "kaoqin", "user": "kqopr", "password": "Kq1234"}}}}, "settings": {}, "staticData": {"webhook": {"main": [[{"body": {"content": "我昨天打卡了吗？", "email": "<EMAIL>", "name": "张三"}}]]}}, "pinData": {}}
"""
IntentParserAgent A2A服务器
"""

import uvicorn
import logging
from agents.intent_parser.a2a_agent import intent_parser_a2a_agent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 获取FastAPI应用
app = intent_parser_a2a_agent.app

@app.get("/")
async def root():
    return {
        "message": "IntentParserAgent A2A Server",
        "agent": intent_parser_a2a_agent.name,
        "version": intent_parser_a2a_agent.version
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "agent": intent_parser_a2a_agent.name,
        "service": "intent-parser-a2a"
    }

if __name__ == "__main__":
    logger.info("启动IntentParserAgent A2A服务器...")
    uvicorn.run(
        "server:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )

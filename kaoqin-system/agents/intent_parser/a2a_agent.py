"""
A2A协议兼容的意图识别Agent
"""

import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from core.a2a_base import A2AAgent, Message, Task, TextPart, DataPart
from core.utils.date_parser import date_parser
from core.utils.ollama_client import ollama_client

logger = logging.getLogger(__name__)

class IntentParserA2AAgent(A2AAgent):
    """基于A2A协议的意图识别Agent"""
    
    def __init__(self):
        super().__init__(
            name="IntentParserAgent",
            version="1.0.0",
            description="解析用户查询意图，提取时间信息，生成结构化查询参数"
        )
        
        # 意图识别的Prompt模板
        self.intent_prompt_template = """
你是一个专业的意图识别助手，专门用于分析用户的考勤查询请求。

请分析以下用户消息，识别用户的查询意图和时间信息：

用户消息："{message}"

请按照以下JSON格式返回结果：
{{
    "intent_type": "attendance_check" 或 "unknown",
    "confidence": 0.0到1.0之间的置信度,
    "time_keywords": ["识别到的时间关键词列表"],
    "is_attendance_query": true或false,
    "reasoning": "简短的分析理由"
}}

分析规则：
1. 如果消息包含"打卡"、"考勤"、"上班"、"下班"、"签到"、"签退"等关键词，则为考勤查询
2. 如果消息包含时间相关词汇（今天、昨天、今日、昨日等），提取这些关键词
3. 置信度基于关键词匹配程度和语义清晰度
4. 如果不确定是否为考勤查询，设置为unknown

请只返回JSON格式的结果，不要包含其他内容。
"""
    
    def get_skills(self) -> List[Dict[str, Any]]:
        """返回Agent支持的技能列表"""
        return [
            {
                "name": "parse_intent",
                "description": "解析用户查询意图，提取时间信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "message": {
                            "type": "string",
                            "description": "用户输入的查询消息"
                        }
                    },
                    "required": ["message"]
                },
                "returns": {
                    "type": "object",
                    "properties": {
                        "intent_type": {"type": "string"},
                        "confidence": {"type": "number"},
                        "date_info": {"type": "object"},
                        "query_params": {"type": "object"}
                    }
                }
            }
        ]
    
    async def process_skill(self, skill_name: str, message: Message, task_id: str) -> Task:
        """处理特定技能的请求"""
        if skill_name == "parse_intent":
            return await self._process_parse_intent(message, task_id)
        else:
            raise ValueError(f"Unknown skill: {skill_name}")
    
    async def _process_parse_intent(self, message: Message, task_id: str) -> Task:
        """处理意图识别请求"""
        try:
            # 提取用户消息文本
            user_text = self._extract_text_from_message(message)
            
            logger.info(f"开始意图识别: {user_text}")
            
            # 1. 使用AI进行意图识别
            ai_result = await self._extract_intent_with_ai(user_text)
            
            # 2. 如果AI识别失败，使用规则引擎
            if not ai_result:
                logger.info("AI识别失败，使用规则引擎")
                ai_result = self._extract_intent_with_rules(user_text)
            
            # 3. 解析日期信息
            date_info = date_parser.parse_date_from_text(user_text)
            
            # 4. 生成查询参数
            query_params = None
            if ai_result.get("is_attendance_query", False) and date_info and date_info.get("success"):
                query_params = {
                    "date": date_info["date"],
                    "start_time": date_info["start_time"],
                    "end_time": date_info["end_time"],
                    "query_type": "attendance_check",
                    "original_message": user_text,
                    "confidence": ai_result.get("confidence", 0.0)
                }
            
            # 5. 创建结果artifact
            result_data = {
                "intent_type": ai_result.get("intent_type", "unknown"),
                "confidence": ai_result.get("confidence", 0.0),
                "date_info": date_info,
                "query_params": query_params,
                "extracted_keywords": ai_result.get("time_keywords", []),
                "reasoning": ai_result.get("reasoning", ""),
                "success": True
            }
            
            artifacts = [{
                "artifactId": f"intent_result_{task_id}",
                "name": "intent_analysis_result",
                "parts": [DataPart(data=result_data).dict()]
            }]
            
            # 6. 创建任务
            context_id = message.contextId or task_id
            task = self._create_task(
                task_id=task_id,
                context_id=context_id,
                message=message,
                status="completed",
                artifacts=artifacts
            )
            
            logger.info(f"意图识别完成: 类型={result_data['intent_type']}, 置信度={result_data['confidence']}")
            return task
            
        except Exception as e:
            logger.error(f"意图识别异常: {e}")
            
            # 创建错误任务
            error_data = {
                "success": False,
                "error_message": str(e),
                "intent_type": "unknown",
                "confidence": 0.0
            }
            
            artifacts = [{
                "artifactId": f"intent_error_{task_id}",
                "name": "intent_analysis_error",
                "parts": [DataPart(data=error_data).dict()]
            }]
            
            context_id = message.contextId or task_id
            return self._create_task(
                task_id=task_id,
                context_id=context_id,
                message=message,
                status="failed",
                artifacts=artifacts
            )
    
    def _extract_text_from_message(self, message: Message) -> str:
        """从Message中提取文本内容"""
        text_parts = []
        for part in message.parts:
            if isinstance(part, dict) and part.get("kind") == "text":
                text_parts.append(part.get("text", ""))
            elif hasattr(part, 'kind') and part.kind == "text":
                text_parts.append(part.text)
        return " ".join(text_parts)
    
    async def _extract_intent_with_ai(self, message: str) -> Optional[Dict[str, Any]]:
        """使用AI模型进行意图识别"""
        try:
            # 构建提示
            prompt = self.intent_prompt_template.format(message=message)
            
            # 调用Ollama模型
            response = ollama_client.generate(prompt)
            
            if not response:
                logger.warning("AI意图识别失败：无响应")
                return None
            
            # 尝试解析JSON响应
            try:
                # 提取JSON部分（可能包含其他文本）
                start_idx = response.find('{')
                end_idx = response.rfind('}') + 1
                
                if start_idx >= 0 and end_idx > start_idx:
                    json_str = response[start_idx:end_idx]
                    result = json.loads(json_str)
                    logger.info(f"AI意图识别成功: {result}")
                    return result
                else:
                    logger.warning(f"AI响应中未找到有效JSON: {response}")
                    return None
                    
            except json.JSONDecodeError as e:
                logger.warning(f"AI响应JSON解析失败: {e}, 响应: {response}")
                return None
                
        except Exception as e:
            logger.error(f"AI意图识别异常: {e}")
            return None
    
    def _extract_intent_with_rules(self, message: str) -> Dict[str, Any]:
        """使用规则引擎进行意图识别（作为AI的备选方案）"""
        try:
            # 考勤相关关键词
            attendance_keywords = [
                "打卡", "考勤", "上班", "下班", "签到", "签退",
                "迟到", "早退", "出勤", "缺勤", "请假"
            ]
            
            # 时间相关关键词
            time_keywords = [
                "今天", "今日", "今", "昨天", "昨日", "昨",
                "前天", "前日", "大前天"
            ]
            
            message_lower = message.lower()
            
            # 检查考勤关键词
            found_attendance_keywords = [kw for kw in attendance_keywords if kw in message_lower]
            found_time_keywords = [kw for kw in time_keywords if kw in message_lower]
            
            # 计算置信度
            confidence = 0.0
            if found_attendance_keywords:
                confidence += 0.7
            if found_time_keywords:
                confidence += 0.2
            if "?" in message or "？" in message or "吗" in message:
                confidence += 0.1
            
            # 确定意图类型
            intent_type = "attendance_check" if confidence >= 0.5 else "unknown"
            
            result = {
                "intent_type": intent_type,
                "confidence": min(confidence, 1.0),
                "time_keywords": found_time_keywords,
                "is_attendance_query": intent_type == "attendance_check",
                "reasoning": f"规则匹配: 考勤词{len(found_attendance_keywords)}个, 时间词{len(found_time_keywords)}个"
            }
            
            logger.info(f"规则意图识别结果: {result}")
            return result
            
        except Exception as e:
            logger.error(f"规则意图识别异常: {e}")
            return {
                "intent_type": "unknown",
                "confidence": 0.0,
                "time_keywords": [],
                "is_attendance_query": False,
                "reasoning": f"识别异常: {str(e)}"
            }
    
    def _determine_skill(self, message: Message) -> Optional[str]:
        """根据消息内容确定要使用的技能"""
        # IntentParserAgent只有一个技能
        return "parse_intent"

# 全局Agent实例
intent_parser_a2a_agent = IntentParserA2AAgent()

"""
意图识别Agent
负责解析用户查询意图，提取时间信息，生成结构化查询参数
"""

import json
import logging
from typing import Optional, Dict, Any
from datetime import datetime

from core.models import AgentMessage, IntentResult, QueryParams, QueryType
from core.utils.date_parser import date_parser
from core.utils.ollama_client import ollama_client

logger = logging.getLogger(__name__)

class IntentParserAgent:
    """意图识别Agent"""
    
    def __init__(self):
        self.agent_name = "IntentParserAgent"
        self.version = "1.0.0"
        
        # 意图识别的Prompt模板
        self.intent_prompt_template = """
你是一个专业的意图识别助手，专门用于分析用户的考勤查询请求。

请分析以下用户消息，识别用户的查询意图和时间信息：

用户消息："{message}"

请按照以下JSON格式返回结果：
{{
    "intent_type": "attendance_check" 或 "unknown",
    "confidence": 0.0到1.0之间的置信度,
    "time_keywords": ["识别到的时间关键词列表"],
    "is_attendance_query": true或false,
    "reasoning": "简短的分析理由"
}}

分析规则：
1. 如果消息包含"打卡"、"考勤"、"上班"、"下班"、"签到"、"签退"等关键词，则为考勤查询
2. 如果消息包含时间相关词汇（今天、昨天、今日、昨日等），提取这些关键词
3. 置信度基于关键词匹配程度和语义清晰度
4. 如果不确定是否为考勤查询，设置为unknown

请只返回JSON格式的结果，不要包含其他内容。
"""
    
    def _extract_intent_with_ai(self, message: str) -> Optional[Dict[str, Any]]:
        """
        使用AI模型进行意图识别
        
        Args:
            message: 用户消息
            
        Returns:
            意图识别结果字典或None
        """
        try:
            # 构建提示
            prompt = self.intent_prompt_template.format(message=message)
            
            # 调用Ollama模型
            response = ollama_client.generate(prompt)
            
            if not response:
                logger.warning("AI意图识别失败：无响应")
                return None
            
            # 尝试解析JSON响应
            try:
                # 提取JSON部分（可能包含其他文本）
                start_idx = response.find('{')
                end_idx = response.rfind('}') + 1
                
                if start_idx >= 0 and end_idx > start_idx:
                    json_str = response[start_idx:end_idx]
                    result = json.loads(json_str)
                    logger.info(f"AI意图识别成功: {result}")
                    return result
                else:
                    logger.warning(f"AI响应中未找到有效JSON: {response}")
                    return None
                    
            except json.JSONDecodeError as e:
                logger.warning(f"AI响应JSON解析失败: {e}, 响应: {response}")
                return None
                
        except Exception as e:
            logger.error(f"AI意图识别异常: {e}")
            return None
    
    def _extract_intent_with_rules(self, message: str) -> Dict[str, Any]:
        """
        使用规则引擎进行意图识别（作为AI的备选方案）
        
        Args:
            message: 用户消息
            
        Returns:
            意图识别结果字典
        """
        try:
            # 考勤相关关键词
            attendance_keywords = [
                "打卡", "考勤", "上班", "下班", "签到", "签退",
                "迟到", "早退", "出勤", "缺勤", "请假"
            ]
            
            # 时间相关关键词
            time_keywords = [
                "今天", "今日", "今", "昨天", "昨日", "昨",
                "前天", "前日", "大前天"
            ]
            
            message_lower = message.lower()
            
            # 检查考勤关键词
            found_attendance_keywords = [kw for kw in attendance_keywords if kw in message_lower]
            found_time_keywords = [kw for kw in time_keywords if kw in message_lower]
            
            # 计算置信度
            confidence = 0.0
            if found_attendance_keywords:
                confidence += 0.7
            if found_time_keywords:
                confidence += 0.2
            if "?" in message or "？" in message or "吗" in message:
                confidence += 0.1
            
            # 确定意图类型
            intent_type = "attendance_check" if confidence >= 0.5 else "unknown"
            
            result = {
                "intent_type": intent_type,
                "confidence": min(confidence, 1.0),
                "time_keywords": found_time_keywords,
                "is_attendance_query": intent_type == "attendance_check",
                "reasoning": f"规则匹配: 考勤词{len(found_attendance_keywords)}个, 时间词{len(found_time_keywords)}个"
            }
            
            logger.info(f"规则意图识别结果: {result}")
            return result
            
        except Exception as e:
            logger.error(f"规则意图识别异常: {e}")
            return {
                "intent_type": "unknown",
                "confidence": 0.0,
                "time_keywords": [],
                "is_attendance_query": False,
                "reasoning": f"识别异常: {str(e)}"
            }
    
    def parse_intent(self, message: str) -> IntentResult:
        """
        解析用户消息的意图
        
        Args:
            message: 用户消息
            
        Returns:
            意图识别结果
        """
        try:
            logger.info(f"开始意图识别: {message}")
            
            # 1. 首先尝试使用AI进行意图识别
            ai_result = self._extract_intent_with_ai(message)
            
            # 2. 如果AI识别失败，使用规则引擎
            if not ai_result:
                logger.info("AI识别失败，使用规则引擎")
                rule_result = self._extract_intent_with_rules(message)
                ai_result = rule_result
            
            # 3. 解析日期信息
            date_info = date_parser.parse_date_from_text(message)
            
            # 4. 构建意图识别结果
            intent_type = QueryType.ATTENDANCE_CHECK if ai_result.get("is_attendance_query", False) else QueryType.UNKNOWN
            
            result = IntentResult(
                intent_type=intent_type,
                confidence=ai_result.get("confidence", 0.0),
                date_info=date_info,
                extracted_keywords=ai_result.get("time_keywords", []),
                original_text=message,
                success=True
            )
            
            logger.info(f"意图识别完成: 类型={intent_type}, 置信度={result.confidence}")
            return result
            
        except Exception as e:
            logger.error(f"意图识别异常: {e}")
            return IntentResult(
                intent_type=QueryType.UNKNOWN,
                confidence=0.0,
                original_text=message,
                success=False,
                error_message=str(e)
            )
    
    def generate_query_params(self, agent_message: AgentMessage, intent_result: IntentResult) -> Optional[QueryParams]:
        """
        根据意图识别结果生成查询参数
        
        Args:
            agent_message: Agent消息
            intent_result: 意图识别结果
            
        Returns:
            查询参数或None
        """
        try:
            # 检查是否为有效的考勤查询
            if intent_result.intent_type != QueryType.ATTENDANCE_CHECK:
                logger.warning(f"非考勤查询，跳过参数生成: {intent_result.intent_type}")
                return None
            
            # 检查日期信息
            if not intent_result.date_info or not intent_result.date_info.get("success", False):
                logger.error("日期信息解析失败")
                return None
            
            date_info = intent_result.date_info
            
            # 构建查询参数
            query_params = QueryParams(
                user_email=agent_message.user_info.email,
                date=date_info["date"],
                start_time=date_info["start_time"],
                end_time=date_info["end_time"],
                query_type=intent_result.intent_type,
                original_message=agent_message.content,
                confidence=intent_result.confidence
            )
            
            logger.info(f"查询参数生成成功: {query_params.date}")
            return query_params
            
        except Exception as e:
            logger.error(f"查询参数生成异常: {e}")
            return None
    
    def process_message(self, agent_message: AgentMessage) -> Optional[QueryParams]:
        """
        处理Agent消息的主要接口
        
        Args:
            agent_message: 来自MessageReceiverAgent的消息
            
        Returns:
            查询参数或None
        """
        try:
            logger.info(f"IntentParserAgent开始处理消息: {agent_message.message_id}")
            
            # 1. 解析意图
            intent_result = self.parse_intent(agent_message.content)
            
            # 2. 生成查询参数
            query_params = self.generate_query_params(agent_message, intent_result)
            
            if query_params:
                logger.info(f"消息处理成功: {agent_message.message_id}")
                return query_params
            else:
                logger.warning(f"无法生成查询参数: {agent_message.message_id}")
                return None
                
        except Exception as e:
            logger.error(f"消息处理异常: {e}")
            return None

# 全局Agent实例
intent_parser_agent = IntentParserAgent()

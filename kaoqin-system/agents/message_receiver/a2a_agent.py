"""
A2A协议兼容的消息接收Agent
负责接收企业微信消息，提取用户身份信息，并转换为A2A标准格式
"""

import json
import xml.etree.ElementTree as ET
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from core.a2a_base import A2AAgent, Message, Task, TextPart, DataPart
from core.models import UserInfo, WeChatMessage
from api.wechat.client import wechat_client
from api.wechat.crypto import wechat_crypto

logger = logging.getLogger(__name__)

class MessageReceiverA2AAgent(A2AAgent):
    """基于A2A协议的消息接收Agent"""
    
    def __init__(self):
        super().__init__(
            name="MessageReceiverAgent",
            version="1.0.0",
            description="接收企业微信消息，提取用户身份信息，转换为标准化格式"
        )
    
    def get_skills(self) -> List[Dict[str, Any]]:
        """返回Agent支持的技能列表"""
        return [
            {
                "name": "process_wechat_message",
                "description": "处理企业微信消息，提取用户信息和消息内容",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "xml_data": {
                            "type": "string",
                            "description": "企业微信XML格式消息"
                        }
                    },
                    "required": ["xml_data"]
                },
                "returns": {
                    "type": "object",
                    "properties": {
                        "user_info": {"type": "object"},
                        "message_content": {"type": "string"},
                        "message_metadata": {"type": "object"}
                    }
                }
            },
            {
                "name": "verify_webhook_signature",
                "description": "验证企业微信Webhook签名",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "signature": {"type": "string"},
                        "timestamp": {"type": "string"},
                        "nonce": {"type": "string"},
                        "echo_str": {"type": "string"}
                    },
                    "required": ["signature", "timestamp", "nonce"]
                },
                "returns": {
                    "type": "object",
                    "properties": {
                        "valid": {"type": "boolean"}
                    }
                }
            }
        ]
    
    async def process_skill(self, skill_name: str, message: Message, task_id: str) -> Task:
        """处理特定技能的请求"""
        if skill_name == "process_wechat_message":
            return await self._process_wechat_message(message, task_id)
        elif skill_name == "verify_webhook_signature":
            return await self._verify_webhook_signature(message, task_id)
        else:
            raise ValueError(f"Unknown skill: {skill_name}")
    
    async def _process_wechat_message(self, message: Message, task_id: str) -> Task:
        """处理企业微信消息"""
        try:
            # 提取XML数据
            xml_data = self._extract_xml_from_message(message)
            
            logger.info(f"开始处理企业微信消息: task_id={task_id}")
            
            # 1. 解析XML消息
            wechat_msg = self.parse_xml_message(xml_data)
            if not wechat_msg:
                raise ValueError("XML消息解析失败")
            
            # 2. 只处理文本消息
            if wechat_msg.msg_type != "text":
                logger.info(f"忽略非文本消息: {wechat_msg.msg_type}")
                result_data = {
                    "success": False,
                    "reason": "非文本消息",
                    "message_type": wechat_msg.msg_type
                }
            else:
                # 3. 提取用户信息
                user_info = self.extract_user_info(wechat_msg.from_user)
                if not user_info:
                    raise ValueError("用户信息提取失败")
                
                # 4. 验证用户权限
                if not self.validate_user_permissions(user_info):
                    raise ValueError("用户权限验证失败")
                
                # 5. 生成标准化结果
                result_data = {
                    "success": True,
                    "user_info": {
                        "user_id": user_info.user_id,
                        "name": user_info.name,
                        "email": user_info.email,
                        "department": user_info.department
                    },
                    "message_content": wechat_msg.content,
                    "message_metadata": {
                        "message_id": f"{wechat_msg.from_user}_{wechat_msg.create_time}",
                        "original_msg_type": wechat_msg.msg_type,
                        "agent_id": wechat_msg.agent_id,
                        "to_user": wechat_msg.to_user,
                        "create_time": wechat_msg.create_time,
                        "timestamp": datetime.fromtimestamp(wechat_msg.create_time).isoformat()
                    }
                }
            
            # 6. 创建结果artifact
            artifacts = [{
                "artifactId": f"wechat_message_result_{task_id}",
                "name": "wechat_message_processing_result",
                "parts": [DataPart(data=result_data).dict()]
            }]
            
            # 7. 创建任务
            context_id = message.contextId or task_id
            task = self._create_task(
                task_id=task_id,
                context_id=context_id,
                message=message,
                status="completed",
                artifacts=artifacts
            )
            
            logger.info(f"企业微信消息处理完成: task_id={task_id}, success={result_data.get('success')}")
            return task
            
        except Exception as e:
            logger.error(f"企业微信消息处理异常: {e}")
            
            # 创建错误任务
            error_data = {
                "success": False,
                "error_message": str(e),
                "error_type": "processing_error"
            }
            
            artifacts = [{
                "artifactId": f"wechat_message_error_{task_id}",
                "name": "wechat_message_processing_error",
                "parts": [DataPart(data=error_data).dict()]
            }]
            
            context_id = message.contextId or task_id
            return self._create_task(
                task_id=task_id,
                context_id=context_id,
                message=message,
                status="failed",
                artifacts=artifacts
            )
    
    async def _verify_webhook_signature(self, message: Message, task_id: str) -> Task:
        """验证Webhook签名"""
        try:
            # 提取签名验证参数
            params = self._extract_signature_params_from_message(message)
            
            # 验证签名
            is_valid = wechat_crypto.verify_signature(
                params.get("signature", ""),
                params.get("timestamp", ""),
                params.get("nonce", ""),
                params.get("echo_str", "")
            )
            
            result_data = {
                "valid": is_valid,
                "signature_check": "passed" if is_valid else "failed"
            }
            
            artifacts = [{
                "artifactId": f"signature_verification_{task_id}",
                "name": "webhook_signature_verification",
                "parts": [DataPart(data=result_data).dict()]
            }]
            
            context_id = message.contextId or task_id
            task = self._create_task(
                task_id=task_id,
                context_id=context_id,
                message=message,
                status="completed",
                artifacts=artifacts
            )
            
            logger.info(f"Webhook签名验证完成: task_id={task_id}, valid={is_valid}")
            return task
            
        except Exception as e:
            logger.error(f"Webhook签名验证异常: {e}")
            
            error_data = {
                "valid": False,
                "error_message": str(e)
            }
            
            artifacts = [{
                "artifactId": f"signature_error_{task_id}",
                "name": "webhook_signature_error",
                "parts": [DataPart(data=error_data).dict()]
            }]
            
            context_id = message.contextId or task_id
            return self._create_task(
                task_id=task_id,
                context_id=context_id,
                message=message,
                status="failed",
                artifacts=artifacts
            )
    
    def _extract_xml_from_message(self, message: Message) -> str:
        """从Message中提取XML数据"""
        for part in message.parts:
            if isinstance(part, dict):
                if part.get("kind") == "text":
                    return part.get("text", "")
                elif part.get("kind") == "data" and "xml_data" in part.get("data", {}):
                    return part["data"]["xml_data"]
            elif hasattr(part, 'kind'):
                if part.kind == "text":
                    return part.text
                elif part.kind == "data" and hasattr(part, 'data') and "xml_data" in part.data:
                    return part.data["xml_data"]
        return ""
    
    def _extract_signature_params_from_message(self, message: Message) -> Dict[str, str]:
        """从Message中提取签名验证参数"""
        for part in message.parts:
            if isinstance(part, dict) and part.get("kind") == "data":
                return part.get("data", {})
            elif hasattr(part, 'kind') and part.kind == "data":
                return part.data if hasattr(part, 'data') else {}
        return {}
    
    def parse_xml_message(self, xml_data: str) -> Optional[WeChatMessage]:
        """解析XML格式的企业微信消息"""
        try:
            root = ET.fromstring(xml_data)
            
            msg_type = root.find('MsgType').text if root.find('MsgType') is not None else ""
            content = root.find('Content').text if root.find('Content') is not None else ""
            from_user = root.find('FromUserName').text if root.find('FromUserName') is not None else ""
            to_user = root.find('ToUserName').text if root.find('ToUserName') is not None else ""
            agent_id = root.find('AgentID').text if root.find('AgentID') is not None else ""
            create_time = int(root.find('CreateTime').text) if root.find('CreateTime') is not None else 0
            
            return WeChatMessage(
                msg_type=msg_type,
                content=content,
                from_user=from_user,
                to_user=to_user,
                agent_id=agent_id,
                create_time=create_time
            )
        except Exception as e:
            logger.error(f"XML消息解析失败: {e}")
            return None
    
    def extract_user_info(self, user_id: str) -> Optional[UserInfo]:
        """提取用户身份信息"""
        try:
            user_data = wechat_client.get_user_info(user_id)
            
            if not user_data:
                logger.warning(f"无法获取用户信息: {user_id}")
                return None
            
            return UserInfo(
                user_id=user_data.get("userid", ""),
                name=user_data.get("name", ""),
                email=user_data.get("email", ""),
                department=user_data.get("department", [{}])[0].get("name", "") if user_data.get("department") else None
            )
        except Exception as e:
            logger.error(f"用户信息提取失败: {e}")
            return None
    
    def validate_user_permissions(self, user_info: UserInfo) -> bool:
        """验证用户权限"""
        try:
            # 基础验证：用户必须有邮箱
            if not user_info.email:
                logger.warning(f"用户无邮箱信息: {user_info.user_id}")
                return False
            
            # 可以添加更多权限验证逻辑
            # 例如：部门限制、用户状态检查等
            
            return True
        except Exception as e:
            logger.error(f"权限验证异常: {e}")
            return False
    
    def _determine_skill(self, message: Message) -> Optional[str]:
        """根据消息内容确定要使用的技能"""
        # 检查是否包含签名验证参数
        for part in message.parts:
            if isinstance(part, dict) and part.get("kind") == "data":
                data = part.get("data", {})
                if "signature" in data and "timestamp" in data and "nonce" in data:
                    return "verify_webhook_signature"
        
        # 默认为消息处理
        return "process_wechat_message"

# 全局Agent实例
message_receiver_a2a_agent = MessageReceiverA2AAgent()

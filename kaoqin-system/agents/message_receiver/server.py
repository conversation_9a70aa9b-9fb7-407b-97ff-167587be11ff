"""
MessageReceiverAgent A2A服务器
"""

import uvicorn
import logging
from agents.message_receiver.a2a_agent import message_receiver_a2a_agent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 获取FastAPI应用
app = message_receiver_a2a_agent.app

@app.get("/")
async def root():
    return {
        "message": "MessageReceiverAgent A2A Server",
        "agent": message_receiver_a2a_agent.name,
        "version": message_receiver_a2a_agent.version
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "agent": message_receiver_a2a_agent.name,
        "service": "message-receiver-a2a"
    }

if __name__ == "__main__":
    logger.info("启动MessageReceiverAgent A2A服务器...")
    uvicorn.run(
        "server:app",
        host="0.0.0.0",
        port=8002,
        reload=True,
        log_level="info"
    )

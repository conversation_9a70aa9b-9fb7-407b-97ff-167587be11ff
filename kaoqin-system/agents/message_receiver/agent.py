import json
import xml.etree.ElementTree as ET
from typing import Optional, Dict, Any
from datetime import datetime
from core.models import UserInfo, WeChatMessage, AgentMessage
from api.wechat.client import wechat_client
from api.wechat.crypto import wechat_crypto
import logging

logger = logging.getLogger(__name__)

class MessageReceiverAgent:
    """消息接收Agent"""
    
    def __init__(self):
        self.agent_name = "MessageReceiverAgent"
    
    def parse_xml_message(self, xml_data: str) -> Optional[WeChatMessage]:
        """解析XML格式的企业微信消息"""
        try:
            root = ET.fromstring(xml_data)
            
            msg_type = root.find('MsgType').text if root.find('MsgType') is not None else ""
            content = root.find('Content').text if root.find('Content') is not None else ""
            from_user = root.find('FromUserName').text if root.find('FromUserName') is not None else ""
            to_user = root.find('ToUserName').text if root.find('ToUserName') is not None else ""
            agent_id = root.find('AgentID').text if root.find('AgentID') is not None else ""
            create_time = int(root.find('CreateTime').text) if root.find('CreateTime') is not None else 0
            
            return WeChatMessage(
                msg_type=msg_type,
                content=content,
                from_user=from_user,
                to_user=to_user,
                agent_id=agent_id,
                create_time=create_time
            )
        except Exception as e:
            logger.error(f"XML消息解析失败: {e}")
            return None
    
    def extract_user_info(self, user_id: str) -> Optional[UserInfo]:
        """提取用户身份信息"""
        try:
            user_data = wechat_client.get_user_info(user_id)
            
            if not user_data:
                logger.warning(f"无法获取用户信息: {user_id}")
                return None
            
            return UserInfo(
                user_id=user_data.get("userid", ""),
                name=user_data.get("name", ""),
                email=user_data.get("email", ""),
                department=user_data.get("department", [{}])[0].get("name", "") if user_data.get("department") else None
            )
        except Exception as e:
            logger.error(f"用户信息提取失败: {e}")
            return None
    
    def validate_user_permissions(self, user_info: UserInfo) -> bool:
        """验证用户权限"""
        try:
            # 基础验证：用户必须有邮箱
            if not user_info.email:
                logger.warning(f"用户无邮箱信息: {user_info.user_id}")
                return False
            
            # 可以添加更多权限验证逻辑
            # 例如：部门限制、用户状态检查等
            
            return True
        except Exception as e:
            logger.error(f"权限验证异常: {e}")
            return False
    
    def process_message(self, xml_data: str) -> Optional[AgentMessage]:
        """处理企业微信消息"""
        try:
            # 1. 解析消息
            wechat_msg = self.parse_xml_message(xml_data)
            if not wechat_msg:
                return None
            
            # 2. 只处理文本消息
            if wechat_msg.msg_type != "text":
                logger.info(f"忽略非文本消息: {wechat_msg.msg_type}")
                return None
            
            # 3. 提取用户信息
            user_info = self.extract_user_info(wechat_msg.from_user)
            if not user_info:
                return None
            
            # 4. 验证用户权限
            if not self.validate_user_permissions(user_info):
                return None
            
            # 5. 生成标准化消息
            agent_message = AgentMessage(
                message_id=f"{wechat_msg.from_user}_{wechat_msg.create_time}",
                agent_name=self.agent_name,
                user_info=user_info,
                content=wechat_msg.content,
                timestamp=datetime.fromtimestamp(wechat_msg.create_time),
                metadata={
                    "original_msg_type": wechat_msg.msg_type,
                    "agent_id": wechat_msg.agent_id,
                    "to_user": wechat_msg.to_user
                }
            )
            
            logger.info(f"消息处理成功: {agent_message.message_id}")
            return agent_message
            
        except Exception as e:
            logger.error(f"消息处理异常: {e}")
            return None
    
    def verify_webhook_signature(self, signature: str, timestamp: str, nonce: str, echo_str: str = "") -> bool:
        """验证Webhook签名"""
        return wechat_crypto.verify_signature(signature, timestamp, nonce, echo_str)

# 全局Agent实例
message_receiver_agent = MessageReceiverAgent()
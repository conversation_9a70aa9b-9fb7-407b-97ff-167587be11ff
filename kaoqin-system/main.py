from fastapi import FastAPI
from api.wechat.webhook import router as wechat_router
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

app = FastAPI(
    title="企业微信考勤查询系统",
    description="基于A2A协议的多Agent考勤查询系统",
    version="1.0.0"
)

# 注册路由
app.include_router(wechat_router, prefix="/api/wechat", tags=["企业微信"])

@app.get("/")
async def root():
    return {"message": "企业微信考勤查询系统运行中"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "kaoqin-system"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
#!/usr/bin/env python3
"""
启动A2A协议考勤查询系统
"""

import subprocess
import time
import sys
import os
import signal
from typing import List

class ServiceManager:
    """服务管理器"""
    
    def __init__(self):
        self.processes: List[subprocess.Popen] = []
    
    def start_service(self, name: str, command: List[str], cwd: str = None):
        """启动服务"""
        print(f"启动 {name}...")
        try:
            process = subprocess.Popen(
                command,
                cwd=cwd or os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            self.processes.append(process)
            print(f"✓ {name} 已启动 (PID: {process.pid})")
            return process
        except Exception as e:
            print(f"✗ {name} 启动失败: {e}")
            return None
    
    def stop_all(self):
        """停止所有服务"""
        print("\n停止所有服务...")
        for process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"✓ 进程 {process.pid} 已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                print(f"✓ 进程 {process.pid} 已强制停止")
            except Exception as e:
                print(f"✗ 停止进程 {process.pid} 失败: {e}")
    
    def wait_for_service(self, url: str, timeout: int = 30):
        """等待服务启动"""
        import requests
        
        for i in range(timeout):
            try:
                response = requests.get(url, timeout=1)
                if response.status_code == 200:
                    return True
            except:
                pass
            time.sleep(1)
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("启动A2A协议考勤查询系统")
    print("=" * 60)
    
    manager = ServiceManager()
    
    # 注册信号处理器
    def signal_handler(sig, frame):
        print("\n收到停止信号...")
        manager.stop_all()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 1. 启动MessageReceiverAgent A2A服务器
        message_receiver_process = manager.start_service(
            "MessageReceiverAgent A2A Server",
            [sys.executable, "agents/message_receiver/server.py"],
            cwd=os.path.dirname(os.path.abspath(__file__))
        )

        if not message_receiver_process:
            print("✗ MessageReceiverAgent启动失败，退出")
            return False

        # 等待服务启动
        print("等待MessageReceiverAgent启动...")
        if manager.wait_for_service("http://localhost:8002/health"):
            print("✓ MessageReceiverAgent已就绪")
        else:
            print("✗ MessageReceiverAgent启动超时")
            return False

        # 2. 启动IntentParserAgent A2A服务器
        intent_process = manager.start_service(
            "IntentParserAgent A2A Server",
            [sys.executable, "agents/intent_parser/server.py"],
            cwd=os.path.dirname(os.path.abspath(__file__))
        )

        if not intent_process:
            print("✗ IntentParserAgent启动失败，退出")
            return False

        # 等待服务启动
        print("等待IntentParserAgent启动...")
        if manager.wait_for_service("http://localhost:8001/health"):
            print("✓ IntentParserAgent已就绪")
        else:
            print("✗ IntentParserAgent启动超时")
            return False
        
        # 3. 启动主系统服务器
        main_process = manager.start_service(
            "Main System Server",
            [sys.executable, "main.py"],
            cwd=os.path.dirname(os.path.abspath(__file__))
        )

        if not main_process:
            print("✗ 主系统启动失败，退出")
            return False

        # 等待主系统启动
        print("等待主系统启动...")
        if manager.wait_for_service("http://localhost:8000/health"):
            print("✓ 主系统已就绪")
        else:
            print("✗ 主系统启动超时")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 A2A协议考勤查询系统启动成功！")
        print("=" * 60)
        print("服务状态:")
        print("  - MessageReceiverAgent A2A Server: http://localhost:8002")
        print("    - Agent Card: http://localhost:8002/.well-known/agent.json")
        print("    - Health Check: http://localhost:8002/health")
        print("  - IntentParserAgent A2A Server: http://localhost:8001")
        print("    - Agent Card: http://localhost:8001/.well-known/agent.json")
        print("    - Health Check: http://localhost:8001/health")
        print("  - Main System Server: http://localhost:8000")
        print("    - Health Check: http://localhost:8000/health")
        print("    - 企业微信Webhook: http://localhost:8000/api/wechat/webhook")
        print("\n测试命令:")
        print("  python test_a2a_protocol.py")
        print("\n按 Ctrl+C 停止所有服务")
        
        # 保持运行
        while True:
            time.sleep(1)
            
            # 检查进程状态
            for i, process in enumerate(manager.processes):
                if process.poll() is not None:
                    print(f"\n⚠ 进程 {process.pid} 已退出")
                    # 可以在这里添加重启逻辑
        
    except KeyboardInterrupt:
        print("\n收到停止信号...")
    except Exception as e:
        print(f"\n✗ 系统运行异常: {e}")
    finally:
        manager.stop_all()
        print("系统已停止")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

# generated by datamodel-codegen:
#   filename:  https://raw.githubusercontent.com/a2aproject/A2A/refs/heads/main/specification/json/a2a.json

from __future__ import annotations

from enum import Enum
from typing import Any, Literal

from pydantic import Field, RootModel

from a2a._base import A2ABaseModel


class A2A(RootModel[Any]):
    root: Any


class In(str, Enum):
    """
    The location of the API key.
    """

    cookie = 'cookie'
    header = 'header'
    query = 'query'


class APIKeySecurityScheme(A2ABaseModel):
    """
    Defines a security scheme using an API key.
    """

    description: str | None = None
    """
    An optional description for the security scheme.
    """
    in_: In
    """
    The location of the API key.
    """
    name: str
    """
    The name of the header, query, or cookie parameter to be used.
    """
    type: Literal['apiKey'] = 'apiKey'
    """
    The type of the security scheme. Must be 'apiKey'.
    """


class AgentExtension(A2ABaseModel):
    """
    A declaration of a protocol extension supported by an Agent.
    """

    description: str | None = None
    """
    A human-readable description of how this agent uses the extension.
    """
    params: dict[str, Any] | None = None
    """
    Optional, extension-specific configuration parameters.
    """
    required: bool | None = None
    """
    If true, the client must understand and comply with the extension's requirements
    to interact with the agent.
    """
    uri: str
    """
    The unique URI identifying the extension.
    """


class AgentInterface(A2ABaseModel):
    """
    Declares a combination of a target URL and a transport protocol for interacting with the agent.
    """

    transport: str
    """
    The transport protocol supported at this URL. This is a string to allow for future
    extension. Core supported transports include 'JSONRPC', 'GRPC', and 'HTTP+JSON'.
    """
    url: str
    """
    The URL where this interface is available.
    """


class AgentProvider(A2ABaseModel):
    """
    Represents the service provider of an agent.
    """

    organization: str
    """
    The name of the agent provider's organization.
    """
    url: str
    """
    A URL for the agent provider's website or relevant documentation.
    """


class AgentSkill(A2ABaseModel):
    """
    Represents a distinct capability or function that an agent can perform.
    """

    description: str
    """
    A detailed description of the skill, intended to help clients or users
    understand its purpose and functionality.
    """
    examples: list[str] | None = Field(
        default=None, examples=[['I need a recipe for bread']]
    )
    """
    Example prompts or scenarios that this skill can handle. Provides a hint to
    the client on how to use the skill.
    """
    id: str
    """
    A unique identifier for the agent's skill.
    """
    input_modes: list[str] | None = None
    """
    The set of supported input MIME types for this skill, overriding the agent's defaults.
    """
    name: str
    """
    A human-readable name for the skill.
    """
    output_modes: list[str] | None = None
    """
    The set of supported output MIME types for this skill, overriding the agent's defaults.
    """
    tags: list[str] = Field(
        ..., examples=[['cooking', 'customer support', 'billing']]
    )
    """
    A set of keywords describing the skill's capabilities.
    """


class AuthorizationCodeOAuthFlow(A2ABaseModel):
    """
    Defines configuration details for the OAuth 2.0 Authorization Code flow.
    """

    authorization_url: str
    """
    The authorization URL to be used for this flow.
    This MUST be a URL and use TLS.
    """
    refresh_url: str | None = None
    """
    The URL to be used for obtaining refresh tokens.
    This MUST be a URL and use TLS.
    """
    scopes: dict[str, str]
    """
    The available scopes for the OAuth2 security scheme. A map between the scope
    name and a short description for it.
    """
    token_url: str
    """
    The token URL to be used for this flow.
    This MUST be a URL and use TLS.
    """


class ClientCredentialsOAuthFlow(A2ABaseModel):
    """
    Defines configuration details for the OAuth 2.0 Client Credentials flow.
    """

    refresh_url: str | None = None
    """
    The URL to be used for obtaining refresh tokens. This MUST be a URL.
    """
    scopes: dict[str, str]
    """
    The available scopes for the OAuth2 security scheme. A map between the scope
    name and a short description for it.
    """
    token_url: str
    """
    The token URL to be used for this flow. This MUST be a URL.
    """


class ContentTypeNotSupportedError(A2ABaseModel):
    """
    An A2A-specific error indicating an incompatibility between the requested
    content types and the agent's capabilities.
    """

    code: Literal[-32005] = -32005
    """
    The error code for an unsupported content type.
    """
    data: Any | None = None
    """
    A primitive or structured value containing additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Incompatible content types'
    """
    The error message.
    """


class DataPart(A2ABaseModel):
    """
    Represents a structured data segment (e.g., JSON) within a message or artifact.
    """

    data: dict[str, Any]
    """
    The structured data content.
    """
    kind: Literal['data'] = 'data'
    """
    The type of this part, used as a discriminator. Always 'data'.
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata associated with this part.
    """


class DeleteTaskPushNotificationConfigParams(A2ABaseModel):
    """
    Defines parameters for deleting a specific push notification configuration for a task.
    """

    id: str
    """
    The unique identifier of the task.
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata associated with the request.
    """
    push_notification_config_id: str
    """
    The ID of the push notification configuration to delete.
    """


class DeleteTaskPushNotificationConfigRequest(A2ABaseModel):
    """
    Represents a JSON-RPC request for the `tasks/pushNotificationConfig/delete` method.
    """

    id: str | int
    """
    The identifier for this request.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['tasks/pushNotificationConfig/delete'] = (
        'tasks/pushNotificationConfig/delete'
    )
    """
    The method name. Must be 'tasks/pushNotificationConfig/delete'.
    """
    params: DeleteTaskPushNotificationConfigParams
    """
    The parameters identifying the push notification configuration to delete.
    """


class DeleteTaskPushNotificationConfigSuccessResponse(A2ABaseModel):
    """
    Represents a successful JSON-RPC response for the `tasks/pushNotificationConfig/delete` method.
    """

    id: str | int | None = None
    """
    The identifier established by the client.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: None
    """
    The result is null on successful deletion.
    """


class FileBase(A2ABaseModel):
    """
    Defines base properties for a file.
    """

    mime_type: str | None = None
    """
    The MIME type of the file (e.g., "application/pdf").
    """
    name: str | None = None
    """
    An optional name for the file (e.g., "document.pdf").
    """


class FileWithBytes(A2ABaseModel):
    """
    Represents a file with its content provided directly as a base64-encoded string.
    """

    bytes: str
    """
    The base64-encoded content of the file.
    """
    mime_type: str | None = None
    """
    The MIME type of the file (e.g., "application/pdf").
    """
    name: str | None = None
    """
    An optional name for the file (e.g., "document.pdf").
    """


class FileWithUri(A2ABaseModel):
    """
    Represents a file with its content located at a specific URI.
    """

    mime_type: str | None = None
    """
    The MIME type of the file (e.g., "application/pdf").
    """
    name: str | None = None
    """
    An optional name for the file (e.g., "document.pdf").
    """
    uri: str
    """
    A URL pointing to the file's content.
    """


class GetTaskPushNotificationConfigParams(A2ABaseModel):
    """
    Defines parameters for fetching a specific push notification configuration for a task.
    """

    id: str
    """
    The unique identifier of the task.
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata associated with the request.
    """
    push_notification_config_id: str | None = None
    """
    The ID of the push notification configuration to retrieve.
    """


class HTTPAuthSecurityScheme(A2ABaseModel):
    """
    Defines a security scheme using HTTP authentication.
    """

    bearer_format: str | None = None
    """
    A hint to the client to identify how the bearer token is formatted (e.g., "JWT").
    This is primarily for documentation purposes.
    """
    description: str | None = None
    """
    An optional description for the security scheme.
    """
    scheme: str
    """
    The name of the HTTP Authentication scheme to be used in the Authorization header,
    as defined in RFC7235 (e.g., "Bearer").
    This value should be registered in the IANA Authentication Scheme registry.
    """
    type: Literal['http'] = 'http'
    """
    The type of the security scheme. Must be 'http'.
    """


class ImplicitOAuthFlow(A2ABaseModel):
    """
    Defines configuration details for the OAuth 2.0 Implicit flow.
    """

    authorization_url: str
    """
    The authorization URL to be used for this flow. This MUST be a URL.
    """
    refresh_url: str | None = None
    """
    The URL to be used for obtaining refresh tokens. This MUST be a URL.
    """
    scopes: dict[str, str]
    """
    The available scopes for the OAuth2 security scheme. A map between the scope
    name and a short description for it.
    """


class InternalError(A2ABaseModel):
    """
    An error indicating an internal error on the server.
    """

    code: Literal[-32603] = -32603
    """
    The error code for an internal server error.
    """
    data: Any | None = None
    """
    A primitive or structured value containing additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Internal error'
    """
    The error message.
    """


class InvalidAgentResponseError(A2ABaseModel):
    """
    An A2A-specific error indicating that the agent returned a response that
    does not conform to the specification for the current method.
    """

    code: Literal[-32006] = -32006
    """
    The error code for an invalid agent response.
    """
    data: Any | None = None
    """
    A primitive or structured value containing additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Invalid agent response'
    """
    The error message.
    """


class InvalidParamsError(A2ABaseModel):
    """
    An error indicating that the method parameters are invalid.
    """

    code: Literal[-32602] = -32602
    """
    The error code for an invalid parameters error.
    """
    data: Any | None = None
    """
    A primitive or structured value containing additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Invalid parameters'
    """
    The error message.
    """


class InvalidRequestError(A2ABaseModel):
    """
    An error indicating that the JSON sent is not a valid Request object.
    """

    code: Literal[-32600] = -32600
    """
    The error code for an invalid request.
    """
    data: Any | None = None
    """
    A primitive or structured value containing additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Request payload validation error'
    """
    The error message.
    """


class JSONParseError(A2ABaseModel):
    """
    An error indicating that the server received invalid JSON.
    """

    code: Literal[-32700] = -32700
    """
    The error code for a JSON parse error.
    """
    data: Any | None = None
    """
    A primitive or structured value containing additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Invalid JSON payload'
    """
    The error message.
    """


class JSONRPCError(A2ABaseModel):
    """
    Represents a JSON-RPC 2.0 Error object, included in an error response.
    """

    code: int
    """
    A number that indicates the error type that occurred.
    """
    data: Any | None = None
    """
    A primitive or structured value containing additional information about the error.
    This may be omitted.
    """
    message: str
    """
    A string providing a short description of the error.
    """


class JSONRPCMessage(A2ABaseModel):
    """
    Defines the base structure for any JSON-RPC 2.0 request, response, or notification.
    """

    id: str | int | None = None
    """
    A unique identifier established by the client. It must be a String, a Number, or null.
    The server must reply with the same value in the response. This property is omitted for notifications.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """


class JSONRPCRequest(A2ABaseModel):
    """
    Represents a JSON-RPC 2.0 Request object.
    """

    id: str | int | None = None
    """
    A unique identifier established by the client. It must be a String, a Number, or null.
    The server must reply with the same value in the response. This property is omitted for notifications.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: str
    """
    A string containing the name of the method to be invoked.
    """
    params: dict[str, Any] | None = None
    """
    A structured value holding the parameter values to be used during the method invocation.
    """


class JSONRPCSuccessResponse(A2ABaseModel):
    """
    Represents a successful JSON-RPC 2.0 Response object.
    """

    id: str | int | None = None
    """
    The identifier established by the client.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: Any
    """
    The value of this member is determined by the method invoked on the Server.
    """


class ListTaskPushNotificationConfigParams(A2ABaseModel):
    """
    Defines parameters for listing all push notification configurations associated with a task.
    """

    id: str
    """
    The unique identifier of the task.
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata associated with the request.
    """


class ListTaskPushNotificationConfigRequest(A2ABaseModel):
    """
    Represents a JSON-RPC request for the `tasks/pushNotificationConfig/list` method.
    """

    id: str | int
    """
    The identifier for this request.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['tasks/pushNotificationConfig/list'] = (
        'tasks/pushNotificationConfig/list'
    )
    """
    The method name. Must be 'tasks/pushNotificationConfig/list'.
    """
    params: ListTaskPushNotificationConfigParams
    """
    The parameters identifying the task whose configurations are to be listed.
    """


class Role(str, Enum):
    """
    Identifies the sender of the message. `user` for the client, `agent` for the service.
    """

    agent = 'agent'
    user = 'user'


class MethodNotFoundError(A2ABaseModel):
    """
    An error indicating that the requested method does not exist or is not available.
    """

    code: Literal[-32601] = -32601
    """
    The error code for a method not found error.
    """
    data: Any | None = None
    """
    A primitive or structured value containing additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Method not found'
    """
    The error message.
    """


class OpenIdConnectSecurityScheme(A2ABaseModel):
    """
    Defines a security scheme using OpenID Connect.
    """

    description: str | None = None
    """
    An optional description for the security scheme.
    """
    open_id_connect_url: str
    """
    The OpenID Connect Discovery URL for the OIDC provider's metadata.
    """
    type: Literal['openIdConnect'] = 'openIdConnect'
    """
    The type of the security scheme. Must be 'openIdConnect'.
    """


class PartBase(A2ABaseModel):
    """
    Defines base properties common to all message or artifact parts.
    """

    metadata: dict[str, Any] | None = None
    """
    Optional metadata associated with this part.
    """


class PasswordOAuthFlow(A2ABaseModel):
    """
    Defines configuration details for the OAuth 2.0 Resource Owner Password flow.
    """

    refresh_url: str | None = None
    """
    The URL to be used for obtaining refresh tokens. This MUST be a URL.
    """
    scopes: dict[str, str]
    """
    The available scopes for the OAuth2 security scheme. A map between the scope
    name and a short description for it.
    """
    token_url: str
    """
    The token URL to be used for this flow. This MUST be a URL.
    """


class PushNotificationAuthenticationInfo(A2ABaseModel):
    """
    Defines authentication details for a push notification endpoint.
    """

    credentials: str | None = None
    """
    Optional credentials required by the push notification endpoint.
    """
    schemes: list[str]
    """
    A list of supported authentication schemes (e.g., 'Basic', 'Bearer').
    """


class PushNotificationConfig(A2ABaseModel):
    """
    Defines the configuration for setting up push notifications for task updates.
    """

    authentication: PushNotificationAuthenticationInfo | None = None
    """
    Optional authentication details for the agent to use when calling the notification URL.
    """
    id: str | None = None
    """
    A unique ID for the push notification configuration, created by the server
    to support multiple notification callbacks.
    """
    token: str | None = None
    """
    A unique token for this task or session to validate incoming push notifications.
    """
    url: str
    """
    The callback URL where the agent should send push notifications.
    """


class PushNotificationNotSupportedError(A2ABaseModel):
    """
    An A2A-specific error indicating that the agent does not support push notifications.
    """

    code: Literal[-32003] = -32003
    """
    The error code for when push notifications are not supported.
    """
    data: Any | None = None
    """
    A primitive or structured value containing additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Push Notification is not supported'
    """
    The error message.
    """


class SecuritySchemeBase(A2ABaseModel):
    """
    Defines base properties shared by all security scheme objects.
    """

    description: str | None = None
    """
    An optional description for the security scheme.
    """


class TaskIdParams(A2ABaseModel):
    """
    Defines parameters containing a task ID, used for simple task operations.
    """

    id: str
    """
    The unique identifier of the task.
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata associated with the request.
    """


class TaskNotCancelableError(A2ABaseModel):
    """
    An A2A-specific error indicating that the task is in a state where it cannot be canceled.
    """

    code: Literal[-32002] = -32002
    """
    The error code for a task that cannot be canceled.
    """
    data: Any | None = None
    """
    A primitive or structured value containing additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Task cannot be canceled'
    """
    The error message.
    """


class TaskNotFoundError(A2ABaseModel):
    """
    An A2A-specific error indicating that the requested task ID was not found.
    """

    code: Literal[-32001] = -32001
    """
    The error code for a task not found error.
    """
    data: Any | None = None
    """
    A primitive or structured value containing additional information about the error.
    This may be omitted.
    """
    message: str | None = 'Task not found'
    """
    The error message.
    """


class TaskPushNotificationConfig(A2ABaseModel):
    """
    A container associating a push notification configuration with a specific task.
    """

    push_notification_config: PushNotificationConfig
    """
    The push notification configuration for this task.
    """
    task_id: str
    """
    The ID of the task.
    """


class TaskQueryParams(A2ABaseModel):
    """
    Defines parameters for querying a task, with an option to limit history length.
    """

    history_length: int | None = None
    """
    The number of most recent messages from the task's history to retrieve.
    """
    id: str
    """
    The unique identifier of the task.
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata associated with the request.
    """


class TaskResubscriptionRequest(A2ABaseModel):
    """
    Represents a JSON-RPC request for the `tasks/resubscribe` method, used to resume a streaming connection.
    """

    id: str | int
    """
    The identifier for this request.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['tasks/resubscribe'] = 'tasks/resubscribe'
    """
    The method name. Must be 'tasks/resubscribe'.
    """
    params: TaskIdParams
    """
    The parameters identifying the task to resubscribe to.
    """


class TaskState(str, Enum):
    """
    Defines the lifecycle states of a Task.
    """

    submitted = 'submitted'
    working = 'working'
    input_required = 'input-required'
    completed = 'completed'
    canceled = 'canceled'
    failed = 'failed'
    rejected = 'rejected'
    auth_required = 'auth-required'
    unknown = 'unknown'


class TextPart(A2ABaseModel):
    """
    Represents a text segment within a message or artifact.
    """

    kind: Literal['text'] = 'text'
    """
    The type of this part, used as a discriminator. Always 'text'.
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata associated with this part.
    """
    text: str
    """
    The string content of the text part.
    """


class UnsupportedOperationError(A2ABaseModel):
    """
    An A2A-specific error indicating that the requested operation is not supported by the agent.
    """

    code: Literal[-32004] = -32004
    """
    The error code for an unsupported operation.
    """
    data: Any | None = None
    """
    A primitive or structured value containing additional information about the error.
    This may be omitted.
    """
    message: str | None = 'This operation is not supported'
    """
    The error message.
    """


class A2AError(
    RootModel[
        JSONParseError
        | InvalidRequestError
        | MethodNotFoundError
        | InvalidParamsError
        | InternalError
        | TaskNotFoundError
        | TaskNotCancelableError
        | PushNotificationNotSupportedError
        | UnsupportedOperationError
        | ContentTypeNotSupportedError
        | InvalidAgentResponseError
    ]
):
    root: (
        JSONParseError
        | InvalidRequestError
        | MethodNotFoundError
        | InvalidParamsError
        | InternalError
        | TaskNotFoundError
        | TaskNotCancelableError
        | PushNotificationNotSupportedError
        | UnsupportedOperationError
        | ContentTypeNotSupportedError
        | InvalidAgentResponseError
    )
    """
    A discriminated union of all standard JSON-RPC and A2A-specific error types.
    """


class AgentCapabilities(A2ABaseModel):
    """
    Defines optional capabilities supported by an agent.
    """

    extensions: list[AgentExtension] | None = None
    """
    A list of protocol extensions supported by the agent.
    """
    push_notifications: bool | None = None
    """
    Indicates if the agent supports sending push notifications for asynchronous task updates.
    """
    state_transition_history: bool | None = None
    """
    Indicates if the agent provides a history of state transitions for a task.
    """
    streaming: bool | None = None
    """
    Indicates if the agent supports Server-Sent Events (SSE) for streaming responses.
    """


class CancelTaskRequest(A2ABaseModel):
    """
    Represents a JSON-RPC request for the `tasks/cancel` method.
    """

    id: str | int
    """
    The identifier for this request.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['tasks/cancel'] = 'tasks/cancel'
    """
    The method name. Must be 'tasks/cancel'.
    """
    params: TaskIdParams
    """
    The parameters identifying the task to cancel.
    """


class FilePart(A2ABaseModel):
    """
    Represents a file segment within a message or artifact. The file content can be
    provided either directly as bytes or as a URI.
    """

    file: FileWithBytes | FileWithUri
    """
    The file content, represented as either a URI or as base64-encoded bytes.
    """
    kind: Literal['file'] = 'file'
    """
    The type of this part, used as a discriminator. Always 'file'.
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata associated with this part.
    """


class GetTaskPushNotificationConfigRequest(A2ABaseModel):
    """
    Represents a JSON-RPC request for the `tasks/pushNotificationConfig/get` method.
    """

    id: str | int
    """
    The identifier for this request.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['tasks/pushNotificationConfig/get'] = (
        'tasks/pushNotificationConfig/get'
    )
    """
    The method name. Must be 'tasks/pushNotificationConfig/get'.
    """
    params: TaskIdParams | GetTaskPushNotificationConfigParams
    """
    The parameters for getting a push notification configuration.
    """


class GetTaskPushNotificationConfigSuccessResponse(A2ABaseModel):
    """
    Represents a successful JSON-RPC response for the `tasks/pushNotificationConfig/get` method.
    """

    id: str | int | None = None
    """
    The identifier established by the client.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: TaskPushNotificationConfig
    """
    The result, containing the requested push notification configuration.
    """


class GetTaskRequest(A2ABaseModel):
    """
    Represents a JSON-RPC request for the `tasks/get` method.
    """

    id: str | int
    """
    The identifier for this request.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['tasks/get'] = 'tasks/get'
    """
    The method name. Must be 'tasks/get'.
    """
    params: TaskQueryParams
    """
    The parameters for querying a task.
    """


class JSONRPCErrorResponse(A2ABaseModel):
    """
    Represents a JSON-RPC 2.0 Error Response object.
    """

    error: (
        JSONRPCError
        | JSONParseError
        | InvalidRequestError
        | MethodNotFoundError
        | InvalidParamsError
        | InternalError
        | TaskNotFoundError
        | TaskNotCancelableError
        | PushNotificationNotSupportedError
        | UnsupportedOperationError
        | ContentTypeNotSupportedError
        | InvalidAgentResponseError
    )
    """
    An object describing the error that occurred.
    """
    id: str | int | None = None
    """
    The identifier established by the client.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """


class ListTaskPushNotificationConfigSuccessResponse(A2ABaseModel):
    """
    Represents a successful JSON-RPC response for the `tasks/pushNotificationConfig/list` method.
    """

    id: str | int | None = None
    """
    The identifier established by the client.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: list[TaskPushNotificationConfig]
    """
    The result, containing an array of all push notification configurations for the task.
    """


class MessageSendConfiguration(A2ABaseModel):
    """
    Defines configuration options for a `message/send` or `message/stream` request.
    """

    accepted_output_modes: list[str] | None = None
    """
    A list of output MIME types the client is prepared to accept in the response.
    """
    blocking: bool | None = None
    """
    If true, the client will wait for the task to complete. The server may reject this if the task is long-running.
    """
    history_length: int | None = None
    """
    The number of most recent messages from the task's history to retrieve in the response.
    """
    push_notification_config: PushNotificationConfig | None = None
    """
    Configuration for the agent to send push notifications for updates after the initial response.
    """


class OAuthFlows(A2ABaseModel):
    """
    Defines the configuration for the supported OAuth 2.0 flows.
    """

    authorization_code: AuthorizationCodeOAuthFlow | None = None
    """
    Configuration for the OAuth Authorization Code flow. Previously called accessCode in OpenAPI 2.0.
    """
    client_credentials: ClientCredentialsOAuthFlow | None = None
    """
    Configuration for the OAuth Client Credentials flow. Previously called application in OpenAPI 2.0.
    """
    implicit: ImplicitOAuthFlow | None = None
    """
    Configuration for the OAuth Implicit flow.
    """
    password: PasswordOAuthFlow | None = None
    """
    Configuration for the OAuth Resource Owner Password flow.
    """


class Part(RootModel[TextPart | FilePart | DataPart]):
    root: TextPart | FilePart | DataPart
    """
    A discriminated union representing a part of a message or artifact, which can
    be text, a file, or structured data.
    """


class SetTaskPushNotificationConfigRequest(A2ABaseModel):
    """
    Represents a JSON-RPC request for the `tasks/pushNotificationConfig/set` method.
    """

    id: str | int
    """
    The identifier for this request.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['tasks/pushNotificationConfig/set'] = (
        'tasks/pushNotificationConfig/set'
    )
    """
    The method name. Must be 'tasks/pushNotificationConfig/set'.
    """
    params: TaskPushNotificationConfig
    """
    The parameters for setting the push notification configuration.
    """


class SetTaskPushNotificationConfigSuccessResponse(A2ABaseModel):
    """
    Represents a successful JSON-RPC response for the `tasks/pushNotificationConfig/set` method.
    """

    id: str | int | None = None
    """
    The identifier established by the client.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: TaskPushNotificationConfig
    """
    The result, containing the configured push notification settings.
    """


class Artifact(A2ABaseModel):
    """
    Represents a file, data structure, or other resource generated by an agent during a task.
    """

    artifact_id: str
    """
    A unique identifier for the artifact within the scope of the task.
    """
    description: str | None = None
    """
    An optional, human-readable description of the artifact.
    """
    extensions: list[str] | None = None
    """
    The URIs of extensions that are relevant to this artifact.
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata for extensions. The key is an extension-specific identifier.
    """
    name: str | None = None
    """
    An optional, human-readable name for the artifact.
    """
    parts: list[Part]
    """
    An array of content parts that make up the artifact.
    """


class DeleteTaskPushNotificationConfigResponse(
    RootModel[
        JSONRPCErrorResponse | DeleteTaskPushNotificationConfigSuccessResponse
    ]
):
    root: JSONRPCErrorResponse | DeleteTaskPushNotificationConfigSuccessResponse
    """
    Represents a JSON-RPC response for the `tasks/pushNotificationConfig/delete` method.
    """


class GetTaskPushNotificationConfigResponse(
    RootModel[
        JSONRPCErrorResponse | GetTaskPushNotificationConfigSuccessResponse
    ]
):
    root: JSONRPCErrorResponse | GetTaskPushNotificationConfigSuccessResponse
    """
    Represents a JSON-RPC response for the `tasks/pushNotificationConfig/get` method.
    """


class ListTaskPushNotificationConfigResponse(
    RootModel[
        JSONRPCErrorResponse | ListTaskPushNotificationConfigSuccessResponse
    ]
):
    root: JSONRPCErrorResponse | ListTaskPushNotificationConfigSuccessResponse
    """
    Represents a JSON-RPC response for the `tasks/pushNotificationConfig/list` method.
    """


class Message(A2ABaseModel):
    """
    Represents a single message in the conversation between a user and an agent.
    """

    context_id: str | None = None
    """
    The context identifier for this message, used to group related interactions.
    """
    extensions: list[str] | None = None
    """
    The URIs of extensions that are relevant to this message.
    """
    kind: Literal['message'] = 'message'
    """
    The type of this object, used as a discriminator. Always 'message' for a Message.
    """
    message_id: str
    """
    A unique identifier for the message, typically a UUID, generated by the sender.
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata for extensions. The key is an extension-specific identifier.
    """
    parts: list[Part]
    """
    An array of content parts that form the message body. A message can be
    composed of multiple parts of different types (e.g., text and files).
    """
    reference_task_ids: list[str] | None = None
    """
    A list of other task IDs that this message references for additional context.
    """
    role: Role
    """
    Identifies the sender of the message. `user` for the client, `agent` for the service.
    """
    task_id: str | None = None
    """
    The identifier of the task this message is part of. Can be omitted for the first message of a new task.
    """


class MessageSendParams(A2ABaseModel):
    """
    Defines the parameters for a request to send a message to an agent. This can be used
    to create a new task, continue an existing one, or restart a task.
    """

    configuration: MessageSendConfiguration | None = None
    """
    Optional configuration for the send request.
    """
    message: Message
    """
    The message object being sent to the agent.
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata for extensions.
    """


class OAuth2SecurityScheme(A2ABaseModel):
    """
    Defines a security scheme using OAuth 2.0.
    """

    description: str | None = None
    """
    An optional description for the security scheme.
    """
    flows: OAuthFlows
    """
    An object containing configuration information for the supported OAuth 2.0 flows.
    """
    type: Literal['oauth2'] = 'oauth2'
    """
    The type of the security scheme. Must be 'oauth2'.
    """


class SecurityScheme(
    RootModel[
        APIKeySecurityScheme
        | HTTPAuthSecurityScheme
        | OAuth2SecurityScheme
        | OpenIdConnectSecurityScheme
    ]
):
    root: (
        APIKeySecurityScheme
        | HTTPAuthSecurityScheme
        | OAuth2SecurityScheme
        | OpenIdConnectSecurityScheme
    )
    """
    Defines a security scheme that can be used to secure an agent's endpoints.
    This is a discriminated union type based on the OpenAPI 3.0 Security Scheme Object.
    """


class SendMessageRequest(A2ABaseModel):
    """
    Represents a JSON-RPC request for the `message/send` method.
    """

    id: str | int
    """
    The identifier for this request.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['message/send'] = 'message/send'
    """
    The method name. Must be 'message/send'.
    """
    params: MessageSendParams
    """
    The parameters for sending a message.
    """


class SendStreamingMessageRequest(A2ABaseModel):
    """
    Represents a JSON-RPC request for the `message/stream` method.
    """

    id: str | int
    """
    The identifier for this request.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    method: Literal['message/stream'] = 'message/stream'
    """
    The method name. Must be 'message/stream'.
    """
    params: MessageSendParams
    """
    The parameters for sending a message.
    """


class SetTaskPushNotificationConfigResponse(
    RootModel[
        JSONRPCErrorResponse | SetTaskPushNotificationConfigSuccessResponse
    ]
):
    root: JSONRPCErrorResponse | SetTaskPushNotificationConfigSuccessResponse
    """
    Represents a JSON-RPC response for the `tasks/pushNotificationConfig/set` method.
    """


class TaskArtifactUpdateEvent(A2ABaseModel):
    """
    An event sent by the agent to notify the client that an artifact has been
    generated or updated. This is typically used in streaming models.
    """

    append: bool | None = None
    """
    If true, the content of this artifact should be appended to a previously sent artifact with the same ID.
    """
    artifact: Artifact
    """
    The artifact that was generated or updated.
    """
    context_id: str
    """
    The context ID associated with the task.
    """
    kind: Literal['artifact-update'] = 'artifact-update'
    """
    The type of this event, used as a discriminator. Always 'artifact-update'.
    """
    last_chunk: bool | None = None
    """
    If true, this is the final chunk of the artifact.
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata for extensions.
    """
    task_id: str
    """
    The ID of the task this artifact belongs to.
    """


class TaskStatus(A2ABaseModel):
    """
    Represents the status of a task at a specific point in time.
    """

    message: Message | None = None
    """
    An optional, human-readable message providing more details about the current status.
    """
    state: TaskState
    """
    The current state of the task's lifecycle.
    """
    timestamp: str | None = Field(
        default=None, examples=['2023-10-27T10:00:00Z']
    )
    """
    An ISO 8601 datetime string indicating when this status was recorded.
    """


class TaskStatusUpdateEvent(A2ABaseModel):
    """
    An event sent by the agent to notify the client of a change in a task's status.
    This is typically used in streaming or subscription models.
    """

    context_id: str
    """
    The context ID associated with the task.
    """
    final: bool
    """
    If true, this is the final event in the stream for this interaction.
    """
    kind: Literal['status-update'] = 'status-update'
    """
    The type of this event, used as a discriminator. Always 'status-update'.
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata for extensions.
    """
    status: TaskStatus
    """
    The new status of the task.
    """
    task_id: str
    """
    The ID of the task that was updated.
    """


class A2ARequest(
    RootModel[
        SendMessageRequest
        | SendStreamingMessageRequest
        | GetTaskRequest
        | CancelTaskRequest
        | SetTaskPushNotificationConfigRequest
        | GetTaskPushNotificationConfigRequest
        | TaskResubscriptionRequest
        | ListTaskPushNotificationConfigRequest
        | DeleteTaskPushNotificationConfigRequest
    ]
):
    root: (
        SendMessageRequest
        | SendStreamingMessageRequest
        | GetTaskRequest
        | CancelTaskRequest
        | SetTaskPushNotificationConfigRequest
        | GetTaskPushNotificationConfigRequest
        | TaskResubscriptionRequest
        | ListTaskPushNotificationConfigRequest
        | DeleteTaskPushNotificationConfigRequest
    )
    """
    A discriminated union representing all possible JSON-RPC 2.0 requests supported by the A2A specification.
    """


class AgentCard(A2ABaseModel):
    """
    The AgentCard is a self-describing manifest for an agent. It provides essential
    metadata including the agent's identity, capabilities, skills, supported
    communication methods, and security requirements.
    """

    additional_interfaces: list[AgentInterface] | None = None
    """
    A list of additional supported interfaces (transport and URL combinations).
    A client can use any of these to communicate with the agent.
    """
    capabilities: AgentCapabilities
    """
    A declaration of optional capabilities supported by the agent.
    """
    default_input_modes: list[str]
    """
    Default set of supported input MIME types for all skills, which can be
    overridden on a per-skill basis.
    """
    default_output_modes: list[str]
    """
    Default set of supported output MIME types for all skills, which can be
    overridden on a per-skill basis.
    """
    description: str = Field(
        ..., examples=['Agent that helps users with recipes and cooking.']
    )
    """
    A human-readable description of the agent, assisting users and other agents
    in understanding its purpose.
    """
    documentation_url: str | None = None
    """
    An optional URL to the agent's documentation.
    """
    icon_url: str | None = None
    """
    An optional URL to an icon for the agent.
    """
    name: str = Field(..., examples=['Recipe Agent'])
    """
    A human-readable name for the agent.
    """
    preferred_transport: str | None = None
    """
    The transport protocol for the preferred endpoint. Defaults to 'JSONRPC' if not specified.
    """
    protocol_version: str | None = '0.2.6'
    """
    The version of the A2A protocol this agent supports.
    """
    provider: AgentProvider | None = None
    """
    Information about the agent's service provider.
    """
    security: list[dict[str, list[str]]] | None = None
    """
    A list of security requirement objects that apply to all agent interactions. Each object
    lists security schemes that can be used. Follows the OpenAPI 3.0 Security Requirement Object.
    """
    security_schemes: dict[str, SecurityScheme] | None = None
    """
    A declaration of the security schemes available to authorize requests. The key is the
    scheme name. Follows the OpenAPI 3.0 Security Scheme Object.
    """
    skills: list[AgentSkill]
    """
    The set of skills, or distinct capabilities, that the agent can perform.
    """
    supports_authenticated_extended_card: bool | None = None
    """
    If true, the agent can provide an extended agent card with additional details
    to authenticated users. Defaults to false.
    """
    url: str
    """
    The preferred endpoint URL for interacting with the agent.
    """
    version: str = Field(..., examples=['1.0.0'])
    """
    The agent's own version number. The format is defined by the provider.
    """


class Task(A2ABaseModel):
    """
    Represents a single, stateful operation or conversation between a client and an agent.
    """

    artifacts: list[Artifact] | None = None
    """
    A collection of artifacts generated by the agent during the execution of the task.
    """
    context_id: str
    """
    A server-generated identifier for maintaining context across multiple related tasks or interactions.
    """
    history: list[Message] | None = None
    """
    An array of messages exchanged during the task, representing the conversation history.
    """
    id: str
    """
    A unique identifier for the task, generated by the client for a new task or provided by the agent.
    """
    kind: Literal['task'] = 'task'
    """
    The type of this object, used as a discriminator. Always 'task' for a Task.
    """
    metadata: dict[str, Any] | None = None
    """
    Optional metadata for extensions. The key is an extension-specific identifier.
    """
    status: TaskStatus
    """
    The current status of the task, including its state and a descriptive message.
    """


class CancelTaskSuccessResponse(A2ABaseModel):
    """
    Represents a successful JSON-RPC response for the `tasks/cancel` method.
    """

    id: str | int | None = None
    """
    The identifier established by the client.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: Task
    """
    The result, containing the final state of the canceled Task object.
    """


class GetTaskSuccessResponse(A2ABaseModel):
    """
    Represents a successful JSON-RPC response for the `tasks/get` method.
    """

    id: str | int | None = None
    """
    The identifier established by the client.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: Task
    """
    The result, containing the requested Task object.
    """


class SendMessageSuccessResponse(A2ABaseModel):
    """
    Represents a successful JSON-RPC response for the `message/send` method.
    """

    id: str | int | None = None
    """
    The identifier established by the client.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: Task | Message
    """
    The result, which can be a direct reply Message or the initial Task object.
    """


class SendStreamingMessageSuccessResponse(A2ABaseModel):
    """
    Represents a successful JSON-RPC response for the `message/stream` method.
    The server may send multiple response objects for a single request.
    """

    id: str | int | None = None
    """
    The identifier established by the client.
    """
    jsonrpc: Literal['2.0'] = '2.0'
    """
    The version of the JSON-RPC protocol. MUST be exactly "2.0".
    """
    result: Task | Message | TaskStatusUpdateEvent | TaskArtifactUpdateEvent
    """
    The result, which can be a Message, Task, or a streaming update event.
    """


class CancelTaskResponse(
    RootModel[JSONRPCErrorResponse | CancelTaskSuccessResponse]
):
    root: JSONRPCErrorResponse | CancelTaskSuccessResponse
    """
    Represents a JSON-RPC response for the `tasks/cancel` method.
    """


class GetTaskResponse(RootModel[JSONRPCErrorResponse | GetTaskSuccessResponse]):
    root: JSONRPCErrorResponse | GetTaskSuccessResponse
    """
    Represents a JSON-RPC response for the `tasks/get` method.
    """


class JSONRPCResponse(
    RootModel[
        JSONRPCErrorResponse
        | SendMessageSuccessResponse
        | SendStreamingMessageSuccessResponse
        | GetTaskSuccessResponse
        | CancelTaskSuccessResponse
        | SetTaskPushNotificationConfigSuccessResponse
        | GetTaskPushNotificationConfigSuccessResponse
        | ListTaskPushNotificationConfigSuccessResponse
        | DeleteTaskPushNotificationConfigSuccessResponse
    ]
):
    root: (
        JSONRPCErrorResponse
        | SendMessageSuccessResponse
        | SendStreamingMessageSuccessResponse
        | GetTaskSuccessResponse
        | CancelTaskSuccessResponse
        | SetTaskPushNotificationConfigSuccessResponse
        | GetTaskPushNotificationConfigSuccessResponse
        | ListTaskPushNotificationConfigSuccessResponse
        | DeleteTaskPushNotificationConfigSuccessResponse
    )
    """
    A discriminated union representing all possible JSON-RPC 2.0 responses
    for the A2A specification methods.
    """


class SendMessageResponse(
    RootModel[JSONRPCErrorResponse | SendMessageSuccessResponse]
):
    root: JSONRPCErrorResponse | SendMessageSuccessResponse
    """
    Represents a JSON-RPC response for the `message/send` method.
    """


class SendStreamingMessageResponse(
    RootModel[JSONRPCErrorResponse | SendStreamingMessageSuccessResponse]
):
    root: JSONRPCErrorResponse | SendStreamingMessageSuccessResponse
    """
    Represents a JSON-RPC response for the `message/stream` method.
    """

#!/usr/bin/env python3
"""
A2A协议测试脚本
验证IntentParserAgent的A2A协议实现
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_agent_card():
    """测试Agent Card获取"""
    print("=" * 50)
    print("测试Agent Card获取")
    print("=" * 50)
    
    try:
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8001/.well-known/agent.json") as response:
                if response.status == 200:
                    agent_card = await response.json()
                    print("✓ Agent Card获取成功:")
                    print(f"  名称: {agent_card.get('name')}")
                    print(f"  版本: {agent_card.get('version')}")
                    print(f"  描述: {agent_card.get('description')}")
                    print(f"  技能数量: {len(agent_card.get('skills', []))}")
                    
                    for skill in agent_card.get('skills', []):
                        print(f"    - {skill.get('name')}: {skill.get('description')}")
                    
                    return True
                else:
                    print(f"✗ Agent Card获取失败: HTTP {response.status}")
                    return False
                    
    except Exception as e:
        print(f"✗ Agent Card获取异常: {e}")
        return False

async def test_message_send():
    """测试message/send RPC调用"""
    print("\n" + "=" * 50)
    print("测试message/send RPC调用")
    print("=" * 50)
    
    test_cases = [
        "我今天打卡了吗？",
        "昨天的考勤记录",
        "今天天气怎么样？",  # 非考勤查询
    ]
    
    try:
        import aiohttp
        
        for i, message_text in enumerate(test_cases, 1):
            print(f"\n测试用例 {i}: {message_text}")
            
            request_data = {
                "jsonrpc": "2.0",
                "id": f"test_{i}",
                "method": "message/send",
                "params": {
                    "message": {
                        "role": "user",
                        "parts": [
                            {
                                "kind": "text",
                                "text": message_text
                            }
                        ],
                        "messageId": f"test_msg_{i}_{datetime.now().timestamp()}"
                    }
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    "http://localhost:8001/a2a/message/send",
                    json=request_data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        if "result" in result:
                            task = result["result"]
                            print(f"  ✓ 任务创建成功: {task.get('id')}")
                            print(f"    状态: {task.get('status', {}).get('state')}")
                            
                            # 检查artifacts
                            artifacts = task.get("artifacts", [])
                            if artifacts:
                                artifact_data = artifacts[0]["parts"][0]["data"]
                                print(f"    意图类型: {artifact_data.get('intent_type')}")
                                print(f"    置信度: {artifact_data.get('confidence')}")
                                
                                if artifact_data.get("query_params"):
                                    qp = artifact_data["query_params"]
                                    print(f"    查询日期: {qp.get('date')}")
                                    print(f"    查询类型: {qp.get('query_type')}")
                                else:
                                    print("    无查询参数生成")
                            else:
                                print("    无artifacts返回")
                        else:
                            print(f"  ✗ 请求失败: {result.get('error')}")
                    else:
                        print(f"  ✗ HTTP请求失败: {response.status}")
                        text = await response.text()
                        print(f"    响应: {text}")
        
        return True
        
    except Exception as e:
        print(f"✗ message/send测试异常: {e}")
        return False

async def test_tasks_get():
    """测试tasks/get RPC调用"""
    print("\n" + "=" * 50)
    print("测试tasks/get RPC调用")
    print("=" * 50)
    
    try:
        import aiohttp
        
        # 首先创建一个任务
        print("1. 创建测试任务...")
        
        create_request = {
            "jsonrpc": "2.0",
            "id": "create_test",
            "method": "message/send",
            "params": {
                "message": {
                    "role": "user",
                    "parts": [{"kind": "text", "text": "我今天打卡了吗？"}],
                    "messageId": f"test_tasks_get_{datetime.now().timestamp()}"
                }
            }
        }
        
        task_id = None
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:8001/a2a/message/send",
                json=create_request
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if "result" in result:
                        task_id = result["result"]["id"]
                        print(f"  ✓ 任务创建成功: {task_id}")
                    else:
                        print(f"  ✗ 任务创建失败: {result}")
                        return False
                else:
                    print(f"  ✗ 任务创建HTTP失败: {response.status}")
                    return False
        
        # 然后获取任务
        print("2. 获取任务状态...")
        
        get_request = {
            "jsonrpc": "2.0",
            "id": "get_test",
            "method": "tasks/get",
            "params": {
                "id": task_id
            }
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:8001/a2a/tasks/get",
                json=get_request
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if "result" in result:
                        task = result["result"]
                        print(f"  ✓ 任务获取成功: {task.get('id')}")
                        print(f"    状态: {task.get('status', {}).get('state')}")
                        print(f"    历史记录数: {len(task.get('history', []))}")
                        print(f"    artifacts数: {len(task.get('artifacts', []))}")
                        return True
                    else:
                        print(f"  ✗ 任务获取失败: {result}")
                        return False
                else:
                    print(f"  ✗ 任务获取HTTP失败: {response.status}")
                    return False
        
    except Exception as e:
        print(f"✗ tasks/get测试异常: {e}")
        return False

async def test_a2a_client():
    """测试A2A客户端"""
    print("\n" + "=" * 50)
    print("测试A2A客户端")
    print("=" * 50)
    
    try:
        from core.a2a_base import A2AClient, Message, TextPart
        
        client = A2AClient()
        
        # 创建测试消息
        message = Message(
            role="user",
            parts=[TextPart(text="我昨天打卡了吗？").dict()],
            messageId=f"client_test_{datetime.now().timestamp()}"
        )
        
        print("发送A2A消息...")
        response = await client.send_message("http://localhost:8001/a2a", message)
        
        if response.get("result"):
            task = response["result"]
            print(f"✓ A2A客户端调用成功: {task.get('id')}")
            
            # 获取任务状态
            print("获取任务状态...")
            task_response = await client.get_task("http://localhost:8001/a2a", task["id"])
            
            if task_response.get("result"):
                print("✓ 任务状态获取成功")
                return True
            else:
                print(f"✗ 任务状态获取失败: {task_response}")
                return False
        else:
            print(f"✗ A2A客户端调用失败: {response}")
            return False
            
    except Exception as e:
        print(f"✗ A2A客户端测试异常: {e}")
        return False

async def main():
    """主测试函数"""
    print("开始A2A协议测试")
    print("时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("\n注意: 请确保IntentParserAgent服务器正在运行 (python agents/intent_parser/server.py)")
    
    results = []
    
    # 测试Agent Card
    results.append(await test_agent_card())
    
    # 测试message/send
    results.append(await test_message_send())
    
    # 测试tasks/get
    results.append(await test_tasks_get())
    
    # 测试A2A客户端
    results.append(await test_a2a_client())
    
    # 总结
    print("\n" + "=" * 50)
    print("A2A协议测试总结")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有A2A协议测试通过！")
        print("✓ Agent Card发布正常")
        print("✓ message/send RPC方法工作正常")
        print("✓ tasks/get RPC方法工作正常")
        print("✓ A2A客户端通信正常")
    else:
        print("⚠ 部分A2A协议测试失败，请检查服务器状态和配置。")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

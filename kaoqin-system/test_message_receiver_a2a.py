#!/usr/bin/env python3
"""
MessageReceiverAgent A2A协议测试脚本
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_message_receiver_agent_card():
    """测试MessageReceiverAgent Card获取"""
    print("=" * 50)
    print("测试MessageReceiverAgent Card获取")
    print("=" * 50)
    
    try:
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8002/.well-known/agent.json") as response:
                if response.status == 200:
                    agent_card = await response.json()
                    print("✓ MessageReceiverAgent Card获取成功:")
                    print(f"  名称: {agent_card.get('name')}")
                    print(f"  版本: {agent_card.get('version')}")
                    print(f"  描述: {agent_card.get('description')}")
                    print(f"  技能数量: {len(agent_card.get('skills', []))}")
                    
                    for skill in agent_card.get('skills', []):
                        print(f"    - {skill.get('name')}: {skill.get('description')}")
                    
                    return True
                else:
                    print(f"✗ MessageReceiverAgent Card获取失败: HTTP {response.status}")
                    return False
                    
    except Exception as e:
        print(f"✗ MessageReceiverAgent Card获取异常: {e}")
        return False

async def test_webhook_signature_verification():
    """测试Webhook签名验证"""
    print("\n" + "=" * 50)
    print("测试Webhook签名验证")
    print("=" * 50)
    
    try:
        import aiohttp
        
        # 模拟签名验证请求
        request_data = {
            "jsonrpc": "2.0",
            "id": "test_signature",
            "method": "message/send",
            "params": {
                "message": {
                    "role": "system",
                    "parts": [
                        {
                            "kind": "data",
                            "data": {
                                "signature": "test_signature",
                                "timestamp": str(int(datetime.now().timestamp())),
                                "nonce": "test_nonce",
                                "echo_str": "test_echo"
                            }
                        }
                    ],
                    "messageId": f"signature_test_{datetime.now().timestamp()}"
                }
            }
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:8002/a2a/message/send",
                json=request_data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    if "result" in result:
                        task = result["result"]
                        print(f"✓ 签名验证任务创建成功: {task.get('id')}")
                        print(f"  状态: {task.get('status', {}).get('state')}")
                        
                        # 检查artifacts
                        artifacts = task.get("artifacts", [])
                        if artifacts:
                            verification_data = artifacts[0]["parts"][0]["data"]
                            print(f"  验证结果: {verification_data.get('valid')}")
                            print(f"  签名检查: {verification_data.get('signature_check')}")
                        else:
                            print("  无artifacts返回")
                        
                        return True
                    else:
                        print(f"✗ 签名验证失败: {result.get('error')}")
                        return False
                else:
                    print(f"✗ HTTP请求失败: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"✗ 签名验证测试异常: {e}")
        return False

async def test_wechat_message_processing():
    """测试企业微信消息处理"""
    print("\n" + "=" * 50)
    print("测试企业微信消息处理")
    print("=" * 50)
    
    # 模拟企业微信XML消息
    test_xml = """<xml>
    <ToUserName><![CDATA[toUser]]></ToUserName>
    <FromUserName><![CDATA[fromUser]]></FromUserName>
    <CreateTime>1348831860</CreateTime>
    <MsgType><![CDATA[text]]></MsgType>
    <Content><![CDATA[我今天打卡了吗？]]></Content>
    <AgentID>1</AgentID>
</xml>"""
    
    try:
        import aiohttp
        
        request_data = {
            "jsonrpc": "2.0",
            "id": "test_wechat_message",
            "method": "message/send",
            "params": {
                "message": {
                    "role": "user",
                    "parts": [
                        {
                            "kind": "text",
                            "text": test_xml
                        }
                    ],
                    "messageId": f"wechat_test_{datetime.now().timestamp()}"
                }
            }
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:8002/a2a/message/send",
                json=request_data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    if "result" in result:
                        task = result["result"]
                        print(f"✓ 消息处理任务创建成功: {task.get('id')}")
                        print(f"  状态: {task.get('status', {}).get('state')}")
                        
                        # 检查artifacts
                        artifacts = task.get("artifacts", [])
                        if artifacts:
                            message_data = artifacts[0]["parts"][0]["data"]
                            print(f"  处理成功: {message_data.get('success')}")
                            
                            if message_data.get("success"):
                                print(f"  消息内容: {message_data.get('message_content')}")
                                user_info = message_data.get('user_info', {})
                                print(f"  用户信息: {user_info.get('name')} ({user_info.get('email')})")
                                metadata = message_data.get('message_metadata', {})
                                print(f"  消息ID: {metadata.get('message_id')}")
                            else:
                                print(f"  处理失败原因: {message_data.get('reason')}")
                        else:
                            print("  无artifacts返回")
                        
                        return True
                    else:
                        print(f"✗ 消息处理失败: {result.get('error')}")
                        return False
                else:
                    print(f"✗ HTTP请求失败: {response.status}")
                    return False
                    
    except Exception as e:
        print(f"✗ 消息处理测试异常: {e}")
        return False

async def test_a2a_client_integration():
    """测试A2A客户端集成"""
    print("\n" + "=" * 50)
    print("测试A2A客户端集成")
    print("=" * 50)
    
    try:
        from core.a2a_base import A2AClient, Message, TextPart
        
        client = A2AClient()
        
        # 创建测试消息
        test_xml = """<xml>
    <ToUserName><![CDATA[toUser]]></ToUserName>
    <FromUserName><![CDATA[testUser]]></FromUserName>
    <CreateTime>1348831860</CreateTime>
    <MsgType><![CDATA[text]]></MsgType>
    <Content><![CDATA[昨天的考勤记录]]></Content>
    <AgentID>1</AgentID>
</xml>"""
        
        message = Message(
            role="user",
            parts=[TextPart(text=test_xml).dict()],
            messageId=f"client_test_{datetime.now().timestamp()}"
        )
        
        print("发送A2A消息到MessageReceiverAgent...")
        response = await client.send_message("http://localhost:8002/a2a", message)
        
        if response.get("result"):
            task = response["result"]
            print(f"✓ A2A客户端调用成功: {task.get('id')}")
            
            # 获取任务状态
            print("获取任务状态...")
            task_response = await client.get_task("http://localhost:8002/a2a", task["id"])
            
            if task_response.get("result"):
                print("✓ 任务状态获取成功")
                task_data = task_response["result"]
                artifacts = task_data.get("artifacts", [])
                if artifacts:
                    result_data = artifacts[0]["parts"][0]["data"]
                    print(f"  处理结果: {result_data.get('success')}")
                return True
            else:
                print(f"✗ 任务状态获取失败: {task_response}")
                return False
        else:
            print(f"✗ A2A客户端调用失败: {response}")
            return False
            
    except Exception as e:
        print(f"✗ A2A客户端测试异常: {e}")
        return False

async def main():
    """主测试函数"""
    print("开始MessageReceiverAgent A2A协议测试")
    print("时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("\n注意: 请确保MessageReceiverAgent服务器正在运行 (python agents/message_receiver/server.py)")
    
    results = []
    
    # 测试Agent Card
    results.append(await test_message_receiver_agent_card())
    
    # 测试签名验证
    results.append(await test_webhook_signature_verification())
    
    # 测试消息处理
    results.append(await test_wechat_message_processing())
    
    # 测试A2A客户端
    results.append(await test_a2a_client_integration())
    
    # 总结
    print("\n" + "=" * 50)
    print("MessageReceiverAgent A2A协议测试总结")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有MessageReceiverAgent A2A协议测试通过！")
        print("✓ Agent Card发布正常")
        print("✓ Webhook签名验证功能正常")
        print("✓ 企业微信消息处理功能正常")
        print("✓ A2A客户端通信正常")
    else:
        print("⚠ 部分MessageReceiverAgent A2A协议测试失败，请检查服务器状态和配置。")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

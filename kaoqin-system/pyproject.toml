[project]
name = "kaoqin-system"
version = "0.1.0"
description = "企业微信考勤查询系统 - 基于A2A协议的多Agent架构"
requires-python = ">=3.10"
dependencies = [
    "fastapi>=0.115.12",
    "uvicorn>=0.25.1",
    "pydantic>=2.6.0",
    "pydantic-settings>=2.1.0",
    "sqlalchemy>=2.0.24",
    "pymysql>=1.1.0",
    "redis>=5.0.2",
    "requests>=2.31.0",
    "python-multipart>=0.0.6",
    "cryptography>=45.0.5",
    "langchain>=0.0.352",
    "python-dotenv>=1.0.0",
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pycryptodome>=3.19.0",
    "a2a-sdk>=0.2.6",
    "aiohttp>=3.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
]

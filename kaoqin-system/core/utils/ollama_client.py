"""
Ollama客户端模块
用于与本地部署的Ollama服务进行交互
"""

import json
import requests
from typing import Optional, Dict, Any, List
import logging
from core.config import settings

logger = logging.getLogger(__name__)

class OllamaClient:
    """Ollama客户端"""
    
    def __init__(self):
        self.base_url = settings.ollama_base_url
        self.model = settings.ollama_model
        self.timeout = 30  # 30秒超时
        self.max_retries = 3
    
    def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        发送HTTP请求到Ollama服务
        
        Args:
            endpoint: API端点
            data: 请求数据
            
        Returns:
            响应数据或None
        """
        url = f"{self.base_url}/{endpoint}"
        
        for attempt in range(self.max_retries):
            try:
                response = requests.post(
                    url,
                    json=data,
                    timeout=self.timeout,
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    logger.warning(f"Ollama请求失败 (尝试 {attempt + 1}/{self.max_retries}): "
                                 f"状态码 {response.status_code}, 响应: {response.text}")
                    
            except requests.exceptions.Timeout:
                logger.warning(f"Ollama请求超时 (尝试 {attempt + 1}/{self.max_retries})")
            except requests.exceptions.ConnectionError:
                logger.warning(f"Ollama连接失败 (尝试 {attempt + 1}/{self.max_retries})")
            except Exception as e:
                logger.error(f"Ollama请求异常 (尝试 {attempt + 1}/{self.max_retries}): {e}")
        
        logger.error(f"Ollama请求最终失败，已重试 {self.max_retries} 次")
        return None
    
    def generate(self, prompt: str, system_prompt: Optional[str] = None) -> Optional[str]:
        """
        生成文本响应
        
        Args:
            prompt: 用户提示
            system_prompt: 系统提示（可选）
            
        Returns:
            生成的文本或None
        """
        try:
            data = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.1,  # 降低随机性，提高一致性
                    "top_p": 0.9,
                    "top_k": 40
                }
            }
            
            if system_prompt:
                data["system"] = system_prompt
            
            logger.info(f"发送Ollama请求: 模型={self.model}, 提示长度={len(prompt)}")
            
            response = self._make_request("api/generate", data)
            
            if response and "response" in response:
                result = response["response"].strip()
                logger.info(f"Ollama响应成功: 响应长度={len(result)}")
                return result
            else:
                logger.error("Ollama响应格式异常")
                return None
                
        except Exception as e:
            logger.error(f"Ollama生成异常: {e}")
            return None
    
    def chat(self, messages: List[Dict[str, str]]) -> Optional[str]:
        """
        聊天模式生成响应
        
        Args:
            messages: 消息列表，格式: [{"role": "user", "content": "..."}]
            
        Returns:
            生成的响应或None
        """
        try:
            data = {
                "model": self.model,
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": 0.1,
                    "top_p": 0.9,
                    "top_k": 40
                }
            }
            
            logger.info(f"发送Ollama聊天请求: 模型={self.model}, 消息数={len(messages)}")
            
            response = self._make_request("api/chat", data)
            
            if response and "message" in response and "content" in response["message"]:
                result = response["message"]["content"].strip()
                logger.info(f"Ollama聊天响应成功: 响应长度={len(result)}")
                return result
            else:
                logger.error("Ollama聊天响应格式异常")
                return None
                
        except Exception as e:
            logger.error(f"Ollama聊天异常: {e}")
            return None
    
    def check_health(self) -> bool:
        """
        检查Ollama服务健康状态
        
        Returns:
            服务是否可用
        """
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                logger.info("Ollama服务健康检查通过")
                return True
            else:
                logger.warning(f"Ollama服务健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"Ollama健康检查异常: {e}")
            return False
    
    def list_models(self) -> Optional[List[str]]:
        """
        获取可用模型列表
        
        Returns:
            模型名称列表或None
        """
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            if response.status_code == 200:
                data = response.json()
                models = [model["name"] for model in data.get("models", [])]
                logger.info(f"获取到 {len(models)} 个可用模型")
                return models
            else:
                logger.error(f"获取模型列表失败: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"获取模型列表异常: {e}")
            return None

# 全局Ollama客户端实例
ollama_client = OllamaClient()

"""
日期解析工具模块
用于解析用户消息中的时间关键词，并转换为标准日期格式
"""

import re
from datetime import datetime, timedelta
from typing import Optional, Tuple, Dict, Any
import logging

logger = logging.getLogger(__name__)

class DateParser:
    """日期解析器"""
    
    def __init__(self):
        # 定义时间关键词映射
        self.time_keywords = {
            "今天": 0,
            "今日": 0,
            "今": 0,
            "昨天": -1,
            "昨日": -1,
            "昨": -1,
            "前天": -2,
            "前日": -2,
            "大前天": -3,
        }
        
        # 编译正则表达式模式
        self.patterns = {
            "relative_day": re.compile(r"(今天|今日|今|昨天|昨日|昨|前天|前日|大前天)", re.IGNORECASE),
            "specific_date": re.compile(r"(\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?)", re.IGNORECASE),
            "month_day": re.compile(r"(\d{1,2}月\d{1,2}[日]?)", re.IGNORECASE),
        }
    
    def extract_date_keywords(self, text: str) -> Dict[str, Any]:
        """
        从文本中提取日期关键词
        
        Args:
            text: 用户输入的文本
            
        Returns:
            包含日期信息的字典
        """
        result = {
            "found_keywords": [],
            "date_type": None,
            "relative_days": None,
            "specific_date": None,
            "confidence": 0.0
        }
        
        try:
            # 1. 检查相对日期关键词
            relative_matches = self.patterns["relative_day"].findall(text)
            if relative_matches:
                keyword = relative_matches[0]
                result["found_keywords"].append(keyword)
                result["date_type"] = "relative"
                result["relative_days"] = self.time_keywords.get(keyword, 0)
                result["confidence"] = 1.0
                logger.info(f"识别到相对日期关键词: {keyword}")
                return result
            
            # 2. 检查具体日期
            specific_matches = self.patterns["specific_date"].findall(text)
            if specific_matches:
                date_str = specific_matches[0]
                result["found_keywords"].append(date_str)
                result["date_type"] = "specific"
                result["specific_date"] = date_str
                result["confidence"] = 0.9
                logger.info(f"识别到具体日期: {date_str}")
                return result
            
            # 3. 检查月日格式
            month_day_matches = self.patterns["month_day"].findall(text)
            if month_day_matches:
                date_str = month_day_matches[0]
                result["found_keywords"].append(date_str)
                result["date_type"] = "month_day"
                result["specific_date"] = date_str
                result["confidence"] = 0.8
                logger.info(f"识别到月日格式: {date_str}")
                return result
            
            # 4. 如果没有找到明确的日期关键词，默认为今天
            logger.warning(f"未识别到明确的日期关键词，默认为今天: {text}")
            result["date_type"] = "default"
            result["relative_days"] = 0
            result["confidence"] = 0.3
            
        except Exception as e:
            logger.error(f"日期关键词提取异常: {e}")
            result["confidence"] = 0.0
        
        return result
    
    def calculate_target_date(self, date_info: Dict[str, Any]) -> Optional[str]:
        """
        根据日期信息计算目标日期
        
        Args:
            date_info: 日期信息字典
            
        Returns:
            格式化的日期字符串 (%Y-%m-%d) 或 None
        """
        try:
            current_date = datetime.now()
            
            if date_info["date_type"] == "relative" or date_info["date_type"] == "default":
                # 相对日期计算
                relative_days = date_info.get("relative_days", 0)
                target_date = current_date + timedelta(days=relative_days)
                return target_date.strftime("%Y-%m-%d")
            
            elif date_info["date_type"] == "specific":
                # 具体日期解析
                date_str = date_info["specific_date"]
                return self._parse_specific_date(date_str, current_date)
            
            elif date_info["date_type"] == "month_day":
                # 月日格式解析
                date_str = date_info["specific_date"]
                return self._parse_month_day(date_str, current_date)
            
            else:
                logger.warning(f"未知的日期类型: {date_info['date_type']}")
                return None
                
        except Exception as e:
            logger.error(f"日期计算异常: {e}")
            return None
    
    def _parse_specific_date(self, date_str: str, current_date: datetime) -> Optional[str]:
        """解析具体日期字符串"""
        try:
            # 处理不同的日期格式
            date_str = date_str.replace("年", "-").replace("月", "-").replace("日", "")
            date_str = date_str.replace("/", "-")
            
            # 尝试解析
            if len(date_str.split("-")) == 3:
                target_date = datetime.strptime(date_str, "%Y-%m-%d")
                return target_date.strftime("%Y-%m-%d")
            
        except Exception as e:
            logger.error(f"具体日期解析失败: {date_str}, {e}")
        
        return None
    
    def _parse_month_day(self, date_str: str, current_date: datetime) -> Optional[str]:
        """解析月日格式"""
        try:
            # 提取月和日
            date_str = date_str.replace("月", "-").replace("日", "")
            parts = date_str.split("-")
            
            if len(parts) == 2:
                month = int(parts[0])
                day = int(parts[1])
                
                # 使用当前年份
                year = current_date.year
                target_date = datetime(year, month, day)
                
                # 如果日期已过，使用下一年
                if target_date < current_date:
                    target_date = datetime(year + 1, month, day)
                
                return target_date.strftime("%Y-%m-%d")
                
        except Exception as e:
            logger.error(f"月日格式解析失败: {date_str}, {e}")
        
        return None
    
    def generate_time_range(self, date_str: str) -> Tuple[str, str]:
        """
        根据日期生成时间范围
        
        Args:
            date_str: 日期字符串 (%Y-%m-%d)
            
        Returns:
            (开始时间, 结束时间) 格式: %Y-%m-%d %H:%M:%S
        """
        try:
            start_time = f"{date_str} 00:00:01"
            end_time = f"{date_str} 23:59:59"
            return start_time, end_time
        except Exception as e:
            logger.error(f"时间范围生成异常: {e}")
            return None, None
    
    def parse_date_from_text(self, text: str) -> Optional[Dict[str, Any]]:
        """
        从文本中解析日期信息的主要接口
        
        Args:
            text: 用户输入的文本
            
        Returns:
            包含完整日期信息的字典
        """
        try:
            # 1. 提取日期关键词
            date_info = self.extract_date_keywords(text)
            
            # 2. 计算目标日期
            target_date = self.calculate_target_date(date_info)
            
            if not target_date:
                logger.error("日期计算失败")
                return None
            
            # 3. 生成时间范围
            start_time, end_time = self.generate_time_range(target_date)
            
            if not start_time or not end_time:
                logger.error("时间范围生成失败")
                return None
            
            # 4. 构建结果
            result = {
                "original_text": text,
                "date": target_date,
                "start_time": start_time,
                "end_time": end_time,
                "keywords": date_info["found_keywords"],
                "date_type": date_info["date_type"],
                "confidence": date_info["confidence"],
                "success": True
            }
            
            logger.info(f"日期解析成功: {text} -> {target_date}")
            return result
            
        except Exception as e:
            logger.error(f"日期解析异常: {e}")
            return {
                "original_text": text,
                "success": False,
                "error": str(e)
            }

# 全局日期解析器实例
date_parser = DateParser()

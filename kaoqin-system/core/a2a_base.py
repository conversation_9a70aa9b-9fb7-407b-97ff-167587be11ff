"""
A2A协议基础框架
提供Agent的基础实现和通用功能
"""

import uuid
import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from abc import ABC, abstractmethod

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

# A2A SDK imports
try:
    from a2a_sdk import AgentCard, AgentSkill, AgentCapabilities, AgentProvider
    from a2a_sdk import Task, Message, TextPart, DataPart, TaskStatus, TaskState
    from a2a_sdk import JSONRPCRequest, JSONRPCResponse, JSONRPCError
except ImportError:
    # 如果a2a-sdk不可用，定义基础数据结构
    class AgentCard(BaseModel):
        name: str
        version: str
        description: str
        url: str
        provider: Dict[str, Any]
        capabilities: Dict[str, Any]
        skills: List[Dict[str, Any]]
    
    class Task(BaseModel):
        id: str
        contextId: Optional[str] = None
        status: Dict[str, Any]
        history: List[Dict[str, Any]] = []
        artifacts: List[Dict[str, Any]] = []
        kind: str = "task"
        metadata: Dict[str, Any] = {}
    
    class Message(BaseModel):
        role: str
        parts: List[Dict[str, Any]]
        messageId: Optional[str] = None
        taskId: Optional[str] = None
        contextId: Optional[str] = None
    
    class TextPart(BaseModel):
        kind: str = "text"
        text: str
    
    class DataPart(BaseModel):
        kind: str = "data"
        data: Dict[str, Any]

logger = logging.getLogger(__name__)

class A2AAgent(ABC):
    """A2A协议Agent基类"""
    
    def __init__(self, name: str, version: str = "1.0.0", description: str = ""):
        self.name = name
        self.version = version
        self.description = description
        self.app = FastAPI(title=f"{name} A2A Agent")
        self.tasks: Dict[str, Task] = {}
        self.contexts: Dict[str, List[str]] = {}  # contextId -> taskIds
        
        # 设置A2A标准路由
        self._setup_routes()
    
    def _setup_routes(self):
        """设置A2A标准路由"""
        
        @self.app.get("/.well-known/agent.json")
        async def get_agent_card():
            """返回Agent Card"""
            return self.get_agent_card()
        
        @self.app.post("/a2a/message/send")
        async def message_send(request: Dict[str, Any]):
            """处理message/send RPC调用"""
            try:
                return await self._handle_message_send(request)
            except Exception as e:
                logger.error(f"message/send处理异常: {e}")
                return self._create_error_response(request.get("id"), -32603, "Internal error")
        
        @self.app.post("/a2a/tasks/get")
        async def tasks_get(request: Dict[str, Any]):
            """处理tasks/get RPC调用"""
            try:
                return await self._handle_tasks_get(request)
            except Exception as e:
                logger.error(f"tasks/get处理异常: {e}")
                return self._create_error_response(request.get("id"), -32603, "Internal error")
    
    @abstractmethod
    def get_skills(self) -> List[Dict[str, Any]]:
        """返回Agent支持的技能列表"""
        pass
    
    @abstractmethod
    async def process_skill(self, skill_name: str, message: Message, task_id: str) -> Task:
        """处理特定技能的请求"""
        pass
    
    def get_agent_card(self) -> Dict[str, Any]:
        """生成Agent Card"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "url": f"http://localhost:8000/a2a",  # 需要根据实际部署配置
            "provider": {
                "name": "KaoQin System",
                "description": "企业微信考勤查询系统"
            },
            "capabilities": {
                "textGeneration": True,
                "dataProcessing": True,
                "pushNotifications": False,
                "streaming": False
            },
            "skills": self.get_skills(),
            "securitySchemes": {
                "none": {
                    "type": "none"
                }
            },
            "security": ["none"]
        }
    
    async def _handle_message_send(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理message/send请求"""
        try:
            # 验证JSON-RPC格式
            if request.get("jsonrpc") != "2.0" or "method" not in request:
                return self._create_error_response(request.get("id"), -32600, "Invalid Request")
            
            if request["method"] != "message/send":
                return self._create_error_response(request.get("id"), -32601, "Method not found")
            
            params = request.get("params", {})
            message_data = params.get("message", {})
            
            # 创建Message对象
            message = Message(**message_data)
            
            # 生成任务ID
            task_id = str(uuid.uuid4())
            context_id = message.contextId or str(uuid.uuid4())
            
            # 确定要调用的技能
            skill_name = self._determine_skill(message)
            if not skill_name:
                return self._create_error_response(
                    request.get("id"), -32004, "No suitable skill found"
                )
            
            # 处理技能请求
            task = await self.process_skill(skill_name, message, task_id)
            
            # 存储任务
            self.tasks[task_id] = task
            
            # 管理上下文
            if context_id not in self.contexts:
                self.contexts[context_id] = []
            self.contexts[context_id].append(task_id)
            
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "result": task.dict()
            }
            
        except Exception as e:
            logger.error(f"message/send处理异常: {e}")
            return self._create_error_response(request.get("id"), -32603, str(e))
    
    async def _handle_tasks_get(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理tasks/get请求"""
        try:
            params = request.get("params", {})
            task_id = params.get("id")
            
            if not task_id:
                return self._create_error_response(request.get("id"), -32602, "Missing task id")
            
            task = self.tasks.get(task_id)
            if not task:
                return self._create_error_response(request.get("id"), -32001, "Task not found")
            
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "result": task.dict()
            }
            
        except Exception as e:
            logger.error(f"tasks/get处理异常: {e}")
            return self._create_error_response(request.get("id"), -32603, str(e))
    
    def _determine_skill(self, message: Message) -> Optional[str]:
        """根据消息内容确定要使用的技能"""
        # 默认实现：返回第一个可用技能
        skills = self.get_skills()
        return skills[0]["name"] if skills else None
    
    def _create_error_response(self, request_id: Any, code: int, message: str) -> Dict[str, Any]:
        """创建错误响应"""
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "error": {
                "code": code,
                "message": message
            }
        }
    
    def _create_task(self, task_id: str, context_id: str, message: Message, 
                     status: str = "completed", artifacts: List[Dict[str, Any]] = None) -> Task:
        """创建Task对象"""
        return Task(
            id=task_id,
            contextId=context_id,
            status={
                "state": status,
                "timestamp": datetime.now().isoformat()
            },
            history=[{
                "role": message.role,
                "parts": [part.dict() if hasattr(part, 'dict') else part for part in message.parts],
                "messageId": message.messageId or str(uuid.uuid4()),
                "taskId": task_id,
                "contextId": context_id
            }],
            artifacts=artifacts or [],
            kind="task",
            metadata={}
        )

class A2AClient:
    """A2A协议客户端"""
    
    def __init__(self):
        self.session = None
    
    async def send_message(self, agent_url: str, message: Message, 
                          context_id: Optional[str] = None) -> Dict[str, Any]:
        """向Agent发送消息"""
        import aiohttp
        
        request_data = {
            "jsonrpc": "2.0",
            "id": str(uuid.uuid4()),
            "method": "message/send",
            "params": {
                "message": {
                    "role": message.role,
                    "parts": [part.dict() if hasattr(part, 'dict') else part for part in message.parts],
                    "messageId": message.messageId or str(uuid.uuid4()),
                    "contextId": context_id
                }
            }
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{agent_url}/message/send", json=request_data) as response:
                return await response.json()
    
    async def get_task(self, agent_url: str, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        import aiohttp
        
        request_data = {
            "jsonrpc": "2.0",
            "id": str(uuid.uuid4()),
            "method": "tasks/get",
            "params": {
                "id": task_id
            }
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{agent_url}/tasks/get", json=request_data) as response:
                return await response.json()

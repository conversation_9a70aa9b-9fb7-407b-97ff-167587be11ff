from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum

class UserInfo(BaseModel):
    """用户信息模型"""
    user_id: str
    name: str
    email: str
    department: Optional[str] = None

class WeChatMessage(BaseModel):
    """企业微信消息模型"""
    msg_type: str
    content: str
    from_user: str
    to_user: str
    agent_id: str
    create_time: int

class AgentMessage(BaseModel):
    """Agent间通信消息模型"""
    message_id: str
    agent_name: str
    user_info: UserInfo
    content: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None

class QueryType(str, Enum):
    """查询类型枚举"""
    ATTENDANCE_CHECK = "attendance_check"  # 考勤查询
    UNKNOWN = "unknown"  # 未知类型

class IntentResult(BaseModel):
    """意图识别结果模型"""
    intent_type: QueryType
    confidence: float
    date_info: Optional[Dict[str, Any]] = None
    extracted_keywords: List[str] = []
    original_text: str
    success: bool
    error_message: Optional[str] = None

class QueryParams(BaseModel):
    """结构化查询参数模型"""
    user_email: str
    date: str  # %Y-%m-%d格式
    start_time: str  # %Y-%m-%d %H:%M:%S格式
    end_time: str  # %Y-%m-%d %H:%M:%S格式
    query_type: QueryType
    original_message: str
    confidence: float
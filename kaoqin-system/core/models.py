from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime

class UserInfo(BaseModel):
    """用户信息模型"""
    user_id: str
    name: str
    email: str
    department: Optional[str] = None

class WeChatMessage(BaseModel):
    """企业微信消息模型"""
    msg_type: str
    content: str
    from_user: str
    to_user: str
    agent_id: str
    create_time: int

class AgentMessage(BaseModel):
    """Agent间通信消息模型"""
    message_id: str
    agent_name: str
    user_info: UserInfo
    content: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None
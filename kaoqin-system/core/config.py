from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # 企业微信配置
    wechat_corp_id: str
    wechat_agent_id: str
    wechat_secret: str
    wechat_token: str
    wechat_encoding_aes_key: str
    
    # 数据库配置
    database_url: str
    
    # Redis配置
    redis_url: str = "redis://localhost:6379/0"
    
    # 考勤接口配置
    attendance_api_url: str
    attendance_api_key: str
    
    # Ollama配置
    ollama_base_url: str = "http://localhost:11434"
    ollama_model: str = "Qwen3:8b"
    
    class Config:
        env_file = ".env"

settings = Settings()
import hashlib
import hmac
import base64
from Crypto.Cipher import AES
from core.config import settings
import logging

logger = logging.getLogger(__name__)

class WeChatCrypto:
    """企业微信消息加解密"""
    
    def __init__(self):
        self.token = settings.wechat_token
        self.encoding_aes_key = settings.wechat_encoding_aes_key
        self.corp_id = settings.wechat_corp_id
    
    def verify_signature(self, signature: str, timestamp: str, nonce: str, echo_str: str = "") -> bool:
        """验证消息签名"""
        try:
            tmp_list = [self.token, timestamp, nonce, echo_str]
            tmp_list.sort()
            tmp_str = "".join(tmp_list)
            
            hash_obj = hashlib.sha1(tmp_str.encode('utf-8'))
            signature_calc = hash_obj.hexdigest()
            
            return signature == signature_calc
        except Exception as e:
            logger.error(f"签名验证异常: {e}")
            return False
    
    def decrypt_message(self, encrypt_msg: str) -> str:
        """解密消息"""
        try:
            # Base64解码
            cipher_text = base64.b64decode(encrypt_msg)
            
            # AES解密
            key = base64.b64decode(self.encoding_aes_key + "=")
            cipher = AES.new(key, AES.MODE_CBC, cipher_text[:16])
            decrypted = cipher.decrypt(cipher_text[16:])
            
            # 去除填充
            pad = decrypted[-1]
            decrypted = decrypted[:-pad]
            
            # 提取消息内容
            content_length = int.from_bytes(decrypted[16:20], byteorder='big')
            content = decrypted[20:20+content_length].decode('utf-8')
            
            return content
        except Exception as e:
            logger.error(f"消息解密异常: {e}")
            return ""

# 全局加密实例
wechat_crypto = WeChatCrypto()
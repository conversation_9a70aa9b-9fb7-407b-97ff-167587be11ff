from fastapi import APIRouter, Request, Query, HTTPException
from fastapi.responses import PlainTextResponse
from agents.message_receiver.agent import message_receiver_agent
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/webhook")
async def verify_webhook(
    msg_signature: str = Query(...),
    timestamp: str = Query(...),
    nonce: str = Query(...),
    echostr: str = Query(...)
):
    """验证企业微信Webhook"""
    try:
        # 验证签名
        if message_receiver_agent.verify_webhook_signature(msg_signature, timestamp, nonce, echostr):
            logger.info("Webhook验证成功")
            return PlainTextResponse(echostr)
        else:
            logger.warning("Webhook验证失败")
            raise HTTPException(status_code=403, detail="签名验证失败")
    except Exception as e:
        logger.error(f"Webhook验证异常: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")

@router.post("/webhook")
async def receive_message(
    request: Request,
    msg_signature: str = Query(...),
    timestamp: str = Query(...),
    nonce: str = Query(...)
):
    """接收企业微信消息"""
    try:
        # 获取请求体
        body = await request.body()
        xml_data = body.decode('utf-8')
        
        logger.info(f"收到消息: {xml_data[:200]}...")
        
        # 验证签名
        if not message_receiver_agent.verify_webhook_signature(msg_signature, timestamp, nonce):
            logger.warning("消息签名验证失败")
            raise HTTPException(status_code=403, detail="签名验证失败")
        
        # 处理消息
        agent_message = message_receiver_agent.process_message(xml_data)
        
        if agent_message:
            # TODO: 将消息发送给下一个Agent (IntentParserAgent)
            logger.info(f"消息处理完成: {agent_message.message_id}")
            
            # 暂时返回成功响应
            return {"status": "success", "message_id": agent_message.message_id}
        else:
            logger.warning("消息处理失败")
            return {"status": "ignored", "reason": "消息处理失败或被忽略"}
            
    except Exception as e:
        logger.error(f"消息接收异常: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")
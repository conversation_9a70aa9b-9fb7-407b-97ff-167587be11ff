from fastapi import APIRouter, Request, Query, HTTPException
from fastapi.responses import PlainTextResponse
from core.a2a_base import A2AClient, Message, TextPart, DataPart
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/webhook")
async def verify_webhook(
    msg_signature: str = Query(...),
    timestamp: str = Query(...),
    nonce: str = Query(...),
    echostr: str = Query(...)
):
    """验证企业微信Webhook"""
    try:
        # 使用A2A协议调用MessageReceiverAgent进行签名验证
        a2a_client = A2AClient()

        # 创建A2A Message
        a2a_message = Message(
            role="system",
            parts=[DataPart(data={
                "signature": msg_signature,
                "timestamp": timestamp,
                "nonce": nonce,
                "echo_str": echostr
            }).dict()],
            messageId=f"webhook_verify_{timestamp}"
        )

        # 调用MessageReceiverAgent
        message_receiver_url = "http://localhost:8002/a2a"
        response = await a2a_client.send_message(message_receiver_url, a2a_message)

        if response.get("result"):
            task_result = response["result"]
            artifacts = task_result.get("artifacts", [])

            if artifacts:
                verification_data = artifacts[0]["parts"][0]["data"]
                if verification_data.get("valid", False):
                    logger.info("Webhook验证成功")
                    return PlainTextResponse(echostr)
                else:
                    logger.warning("Webhook验证失败")
                    raise HTTPException(status_code=403, detail="签名验证失败")
            else:
                logger.error("MessageReceiverAgent返回结果无artifacts")
                raise HTTPException(status_code=500, detail="签名验证服务异常")
        else:
            logger.error(f"MessageReceiverAgent A2A调用失败: {response}")
            raise HTTPException(status_code=500, detail="签名验证服务不可用")

    except Exception as e:
        logger.error(f"Webhook验证异常: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")

@router.post("/webhook")
async def receive_message(
    request: Request,
    msg_signature: str = Query(...),
    timestamp: str = Query(...),
    nonce: str = Query(...)
):
    """接收企业微信消息"""
    try:
        # 获取请求体
        body = await request.body()
        xml_data = body.decode('utf-8')

        logger.info(f"收到消息: {xml_data[:200]}...")

        # 使用A2A协议调用MessageReceiverAgent处理消息
        a2a_client = A2AClient()

        # 创建A2A Message
        a2a_message = Message(
            role="user",
            parts=[TextPart(text=xml_data).dict()],
            messageId=f"wechat_msg_{timestamp}"
        )

        # 调用MessageReceiverAgent
        message_receiver_url = "http://localhost:8002/a2a"
        message_response = await a2a_client.send_message(message_receiver_url, a2a_message)

        if not message_response.get("result"):
            logger.error(f"MessageReceiverAgent A2A调用失败: {message_response}")
            return {"status": "error", "reason": "消息处理服务不可用"}

        message_task_result = message_response["result"]
        message_artifacts = message_task_result.get("artifacts", [])

        if not message_artifacts:
            logger.warning("MessageReceiverAgent返回结果无artifacts")
            return {"status": "error", "reason": "消息处理结果格式异常"}

        message_data = message_artifacts[0]["parts"][0]["data"]

        if not message_data.get("success"):
            logger.info(f"消息处理被忽略: {message_data.get('reason', '未知原因')}")
            return {
                "status": "ignored",
                "reason": message_data.get("reason", "消息处理失败"),
                "message_type": message_data.get("message_type")
            }

        # 消息处理成功，继续调用IntentParserAgent
        logger.info(f"MessageReceiver处理完成: {message_data['message_metadata']['message_id']}")

        try:
            # 创建A2A Message for IntentParserAgent
            intent_message = Message(
                role="user",
                parts=[TextPart(text=message_data["message_content"]).dict()],
                messageId=message_data["message_metadata"]["message_id"]
            )

            # 调用IntentParserAgent
            intent_agent_url = "http://localhost:8001/a2a"
            response = await a2a_client.send_message(intent_agent_url, intent_message)

                if response.get("result"):
                    task_result = response["result"]
                    logger.info(f"IntentParser A2A调用成功: task_id={task_result.get('id')}")

                    # 提取意图识别结果
                    artifacts = task_result.get("artifacts", [])
                    if artifacts:
                        intent_data = artifacts[0]["parts"][0]["data"]

                    if intent_data.get("success") and intent_data.get("query_params"):
                        query_params = intent_data["query_params"]
                        logger.info(f"意图识别成功: 查询日期={query_params['date']}, 置信度={query_params['confidence']}")

                        # TODO: 将查询参数发送给下一个Agent (DataQueryAgent)
                        return {
                            "status": "success",
                            "message_id": message_data["message_metadata"]["message_id"],
                            "task_id": task_result.get("id"),
                            "intent_parsed": True,
                            "query_date": query_params["date"],
                            "confidence": query_params["confidence"],
                            "user_info": message_data["user_info"]
                        }
                    else:
                        logger.warning(f"意图识别失败或非考勤查询: {message_data['message_content']}")
                        return {
                            "status": "ignored",
                            "reason": "意图识别失败或非考勤查询",
                            "message_id": message_data["message_metadata"]["message_id"],
                            "task_id": task_result.get("id")
                        }
                else:
                    logger.warning("IntentParser返回结果无artifacts")
                    return {
                        "status": "error",
                        "reason": "IntentParser返回结果格式异常",
                        "message_id": message_data["message_metadata"]["message_id"]
                    }
            else:
                logger.error(f"IntentParser A2A调用失败: {response}")
                return {
                    "status": "error",
                    "reason": "IntentParser A2A调用失败",
                    "message_id": message_data["message_metadata"]["message_id"]
                }

        except Exception as e:
            logger.error(f"A2A调用异常: {e}")
            return {
                "status": "error",
                "reason": f"A2A调用异常: {str(e)}",
                "message_id": message_data["message_metadata"]["message_id"]
            }
            
    except Exception as e:
        logger.error(f"消息接收异常: {e}")
        raise HTTPException(status_code=500, detail="服务器内部错误")
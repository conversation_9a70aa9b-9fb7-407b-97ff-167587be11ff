import requests
import json
import time
from typing import Optional, Dict, Any
from core.config import settings
import logging

logger = logging.getLogger(__name__)

class WeChatClient:
    """企业微信API客户端"""
    
    def __init__(self):
        self.corp_id = settings.wechat_corp_id
        self.agent_id = settings.wechat_agent_id
        self.secret = settings.wechat_secret
        self.access_token: Optional[str] = None
        self.token_expires_at: int = 0
    
    def get_access_token(self) -> str:
        """获取访问令牌"""
        if self.access_token and time.time() < self.token_expires_at:
            return self.access_token
        
        url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken"
        params = {
            "corpid": self.corp_id,
            "corpsecret": self.secret
        }
        
        try:
            response = requests.get(url, params=params)
            data = response.json()
            
            if data.get("errcode") == 0:
                self.access_token = data["access_token"]
                self.token_expires_at = time.time() + data["expires_in"] - 300  # 提前5分钟刷新
                return self.access_token
            else:
                logger.error(f"获取access_token失败: {data}")
                raise Exception(f"获取access_token失败: {data.get('errmsg')}")
        except Exception as e:
            logger.error(f"获取access_token异常: {e}")
            raise
    
    def get_user_info(self, user_id: str) -> Dict[str, Any]:
        """获取用户详细信息"""
        access_token = self.get_access_token()
        url = f"https://qyapi.weixin.qq.com/cgi-bin/user/get"
        params = {
            "access_token": access_token,
            "userid": user_id
        }
        
        try:
            response = requests.get(url, params=params)
            data = response.json()
            
            if data.get("errcode") == 0:
                return data
            else:
                logger.error(f"获取用户信息失败: {data}")
                return {}
        except Exception as e:
            logger.error(f"获取用户信息异常: {e}")
            return {}
    
    def send_message(self, user_id: str, content: str) -> bool:
        """发送消息给用户"""
        access_token = self.get_access_token()
        url = f"https://qyapi.weixin.qq.com/cgi-bin/message/send"
        
        data = {
            "touser": user_id,
            "msgtype": "text",
            "agentid": self.agent_id,
            "text": {
                "content": content
            }
        }
        
        try:
            response = requests.post(
                url,
                params={"access_token": access_token},
                json=data
            )
            result = response.json()
            
            if result.get("errcode") == 0:
                logger.info(f"消息发送成功: {user_id}")
                return True
            else:
                logger.error(f"消息发送失败: {result}")
                return False
        except Exception as e:
            logger.error(f"消息发送异常: {e}")
            return False

# 全局客户端实例
wechat_client = WeChatClient()
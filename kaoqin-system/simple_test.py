#!/usr/bin/env python3
"""
简单功能测试
"""

from datetime import datetime, timedelta
import re

def test_date_parsing():
    """测试日期解析逻辑"""
    print("测试日期解析...")
    
    # 模拟日期解析逻辑
    def parse_date_simple(text):
        today = datetime.now()
        
        if "今天" in text or "今日" in text:
            return today.strftime("%Y-%m-%d")
        elif "昨天" in text or "昨日" in text:
            return (today - timedelta(days=1)).strftime("%Y-%m-%d")
        else:
            return today.strftime("%Y-%m-%d")  # 默认今天
    
    test_cases = [
        ("我今天打卡了吗？", datetime.now().strftime("%Y-%m-%d")),
        ("昨天的考勤记录", (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")),
        ("今日上班时间", datetime.now().strftime("%Y-%m-%d")),
    ]
    
    for text, expected in test_cases:
        result = parse_date_simple(text)
        status = "✓" if result == expected else "✗"
        print(f"{status} {text} -> {result}")
    
    print("日期解析测试完成\n")

def test_intent_recognition():
    """测试意图识别逻辑"""
    print("测试意图识别...")
    
    # 模拟意图识别逻辑
    def recognize_intent_simple(text):
        attendance_keywords = ["打卡", "考勤", "上班", "下班", "签到", "签退"]
        
        for keyword in attendance_keywords:
            if keyword in text:
                return "attendance_check", 0.8
        
        return "unknown", 0.1
    
    test_cases = [
        ("我今天打卡了吗？", "attendance_check"),
        ("昨天的考勤记录", "attendance_check"),
        ("今天天气怎么样？", "unknown"),
        ("你好", "unknown"),
    ]
    
    for text, expected in test_cases:
        intent, confidence = recognize_intent_simple(text)
        status = "✓" if intent == expected else "✗"
        print(f"{status} {text} -> {intent} (置信度: {confidence})")
    
    print("意图识别测试完成\n")

def test_time_range_generation():
    """测试时间范围生成"""
    print("测试时间范围生成...")
    
    date_str = "2025-07-27"
    start_time = f"{date_str} 00:00:01"
    end_time = f"{date_str} 23:59:59"
    
    print(f"✓ 日期: {date_str}")
    print(f"✓ 开始时间: {start_time}")
    print(f"✓ 结束时间: {end_time}")
    print("时间范围生成测试完成\n")

def main():
    print("=" * 50)
    print("功能二核心逻辑测试")
    print("=" * 50)
    
    test_date_parsing()
    test_intent_recognition()
    test_time_range_generation()
    
    print("=" * 50)
    print("核心逻辑测试完成！")
    print("=" * 50)

if __name__ == "__main__":
    main()

"""
意图识别Agent测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
from core.models import UserInfo, AgentMessage, QueryType
from agents.intent_parser.agent import intent_parser_agent
from core.utils.date_parser import date_parser

class TestDateParser:
    """日期解析器测试"""
    
    def test_today_keywords(self):
        """测试今天关键词识别"""
        test_cases = [
            "我今天打卡了吗？",
            "今日考勤情况",
            "今天上班时间",
            "查看今天的打卡记录"
        ]
        
        for text in test_cases:
            result = date_parser.parse_date_from_text(text)
            assert result is not None
            assert result["success"] is True
            assert result["date"] == datetime.now().strftime("%Y-%m-%d")
            print(f"✓ 今天关键词测试通过: {text} -> {result['date']}")
    
    def test_yesterday_keywords(self):
        """测试昨天关键词识别"""
        test_cases = [
            "我昨天打卡了吗？",
            "昨日考勤情况",
            "昨天上班时间",
            "查看昨天的打卡记录"
        ]
        
        yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
        
        for text in test_cases:
            result = date_parser.parse_date_from_text(text)
            assert result is not None
            assert result["success"] is True
            assert result["date"] == yesterday
            print(f"✓ 昨天关键词测试通过: {text} -> {result['date']}")
    
    def test_time_range_generation(self):
        """测试时间范围生成"""
        date_str = "2025-07-27"
        start_time, end_time = date_parser.generate_time_range(date_str)
        
        assert start_time == "2025-07-27 00:00:01"
        assert end_time == "2025-07-27 23:59:59"
        print(f"✓ 时间范围生成测试通过: {start_time} - {end_time}")

class TestIntentParser:
    """意图识别Agent测试"""
    
    def create_test_message(self, content: str) -> AgentMessage:
        """创建测试消息"""
        user_info = UserInfo(
            user_id="test_user",
            name="测试用户",
            email="<EMAIL>"
        )
        
        return AgentMessage(
            message_id="test_msg_001",
            agent_name="MessageReceiverAgent",
            user_info=user_info,
            content=content,
            timestamp=datetime.now()
        )
    
    def test_attendance_intent_recognition(self):
        """测试考勤意图识别"""
        test_cases = [
            "我今天打卡了吗？",
            "昨天的考勤记录",
            "今天上班时间",
            "我昨天有没有迟到？",
            "查看今天的签到情况"
        ]
        
        for content in test_cases:
            message = self.create_test_message(content)
            intent_result = intent_parser_agent.parse_intent(content)
            
            assert intent_result.success is True
            assert intent_result.intent_type == QueryType.ATTENDANCE_CHECK
            assert intent_result.confidence > 0.5
            print(f"✓ 考勤意图识别测试通过: {content} -> 置信度={intent_result.confidence}")
    
    def test_non_attendance_intent_recognition(self):
        """测试非考勤意图识别"""
        test_cases = [
            "今天天气怎么样？",
            "你好",
            "帮我查询一下",
            "今天是几号？"
        ]
        
        for content in test_cases:
            message = self.create_test_message(content)
            intent_result = intent_parser_agent.parse_intent(content)
            
            assert intent_result.success is True
            # 非考勤查询应该被识别为UNKNOWN或置信度较低
            if intent_result.intent_type == QueryType.ATTENDANCE_CHECK:
                assert intent_result.confidence < 0.7  # 允许一定的误识别，但置信度应该较低
            print(f"✓ 非考勤意图识别测试通过: {content} -> 类型={intent_result.intent_type}, 置信度={intent_result.confidence}")
    
    def test_query_params_generation(self):
        """测试查询参数生成"""
        content = "我今天打卡了吗？"
        message = self.create_test_message(content)
        
        # 处理消息
        query_params = intent_parser_agent.process_message(message)
        
        assert query_params is not None
        assert query_params.user_email == "<EMAIL>"
        assert query_params.date == datetime.now().strftime("%Y-%m-%d")
        assert query_params.query_type == QueryType.ATTENDANCE_CHECK
        assert query_params.start_time.endswith("00:00:01")
        assert query_params.end_time.endswith("23:59:59")
        
        print(f"✓ 查询参数生成测试通过:")
        print(f"  邮箱: {query_params.user_email}")
        print(f"  日期: {query_params.date}")
        print(f"  开始时间: {query_params.start_time}")
        print(f"  结束时间: {query_params.end_time}")
        print(f"  置信度: {query_params.confidence}")

def run_manual_tests():
    """手动运行测试"""
    print("=" * 50)
    print("开始功能二测试：自然语言意图识别与日期解析")
    print("=" * 50)
    
    # 测试日期解析器
    print("\n1. 日期解析器测试")
    print("-" * 30)
    date_test = TestDateParser()
    date_test.test_today_keywords()
    date_test.test_yesterday_keywords()
    date_test.test_time_range_generation()
    
    # 测试意图识别
    print("\n2. 意图识别Agent测试")
    print("-" * 30)
    intent_test = TestIntentParser()
    intent_test.test_attendance_intent_recognition()
    intent_test.test_non_attendance_intent_recognition()
    intent_test.test_query_params_generation()
    
    print("\n" + "=" * 50)
    print("所有测试完成！")
    print("=" * 50)

if __name__ == "__main__":
    run_manual_tests()

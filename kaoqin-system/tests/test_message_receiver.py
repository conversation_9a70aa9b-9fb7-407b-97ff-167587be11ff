import pytest
from unittest.mock import Mock, patch
from agents.message_receiver.agent import Message<PERSON><PERSON><PERSON><PERSON>Agent
from core.models import UserInfo

class TestMessageReceiverAgent:
    
    def setup_method(self):
        self.agent = MessageReceiverAgent()
    
    def test_parse_xml_message_success(self):
        """测试XML消息解析成功"""
        xml_data = """
        <xml>
            <MsgType><![CDATA[text]]></MsgType>
            <Content><![CDATA[我今天打卡了吗？]]></Content>
            <FromUserName><![CDATA[user123]]></FromUserName>
            <ToUserName><![CDATA[app]]></ToUserName>
            <AgentID>1000001</AgentID>
            <CreateTime>1640995200</CreateTime>
        </xml>
        """
        
        result = self.agent.parse_xml_message(xml_data)
        
        assert result is not None
        assert result.msg_type == "text"
        assert result.content == "我今天打卡了吗？"
        assert result.from_user == "user123"
        assert result.create_time == 1640995200
    
    def test_parse_xml_message_invalid(self):
        """测试无效XML消息解析"""
        xml_data = "invalid xml"
        
        result = self.agent.parse_xml_message(xml_data)
        
        assert result is None
    
    @patch('agents.message_receiver.agent.wechat_client.get_user_info')
    def test_extract_user_info_success(self, mock_get_user_info):
        """测试用户信息提取成功"""
        mock_get_user_info.return_value = {
            "userid": "user123",
            "name": "张三",
            "email": "<EMAIL>",
            "department": [{"name": "技术部"}]
        }
        
        result = self.agent.extract_user_info("user123")
        
        assert result is not None
        assert result.user_id == "user123"
        assert result.name == "张三"
        assert result.email == "<EMAIL>"
        assert result.department == "技术部"
    
    @patch('agents.message_receiver.agent.wechat_client.get_user_info')
    def test_extract_user_info_no_data(self, mock_get_user_info):
        """测试用户信息提取失败"""
        mock_get_user_info.return_value = {}
        
        result = self.agent.extract_user_info("user123")
        
        assert result is None
    
    def test_validate_user_permissions_valid(self):
        """测试用户权限验证通过"""
        user_info = UserInfo(
            user_id="user123",
            name="张三",
            email="<EMAIL>"
        )
        
        result = self.agent.validate_user_permissions(user_info)
        
        assert result is True
    
    def test_validate_user_permissions_no_email(self):
        """测试用户权限验证失败（无邮箱）"""
        user_info = UserInfo(
            user_id="user123",
            name="张三",
            email=""
        )
        
        result = self.agent.validate_user_permissions(user_info)
        
        assert result is False

if __name__ == "__main__":
    pytest.main([__file__])
#!/usr/bin/env python3
"""
功能二测试脚本：自然语言意图识别与日期解析
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_date_parser():
    """测试日期解析功能"""
    print("=" * 50)
    print("测试日期解析功能")
    print("=" * 50)
    
    try:
        from core.utils.date_parser import date_parser
        
        test_cases = [
            "我今天打卡了吗？",
            "昨天的考勤记录",
            "今日上班时间",
            "昨日签到情况"
        ]
        
        for text in test_cases:
            print(f"\n测试文本: {text}")
            result = date_parser.parse_date_from_text(text)
            
            if result and result.get("success"):
                print(f"✓ 解析成功:")
                print(f"  日期: {result['date']}")
                print(f"  开始时间: {result['start_time']}")
                print(f"  结束时间: {result['end_time']}")
                print(f"  关键词: {result['keywords']}")
                print(f"  置信度: {result['confidence']}")
            else:
                print(f"✗ 解析失败: {result}")
        
        print("\n✓ 日期解析功能测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 日期解析功能测试失败: {e}")
        return False

def test_intent_parser():
    """测试意图识别功能"""
    print("\n" + "=" * 50)
    print("测试意图识别功能")
    print("=" * 50)
    
    try:
        from core.models import UserInfo, AgentMessage, QueryType
        from agents.intent_parser.agent import intent_parser_agent
        
        # 创建测试用户信息
        user_info = UserInfo(
            user_id="test_user",
            name="测试用户",
            email="<EMAIL>"
        )
        
        test_cases = [
            "我今天打卡了吗？",
            "昨天有没有迟到？",
            "今天的考勤记录",
            "今天天气怎么样？",  # 非考勤查询
            "你好"  # 非考勤查询
        ]
        
        for content in test_cases:
            print(f"\n测试消息: {content}")
            
            # 创建测试消息
            message = AgentMessage(
                message_id=f"test_{hash(content)}",
                agent_name="MessageReceiverAgent",
                user_info=user_info,
                content=content,
                timestamp=datetime.now()
            )
            
            # 测试意图识别
            intent_result = intent_parser_agent.parse_intent(content)
            print(f"  意图类型: {intent_result.intent_type}")
            print(f"  置信度: {intent_result.confidence}")
            print(f"  成功: {intent_result.success}")
            
            # 测试查询参数生成
            if intent_result.intent_type == QueryType.ATTENDANCE_CHECK:
                query_params = intent_parser_agent.generate_query_params(message, intent_result)
                if query_params:
                    print(f"  ✓ 查询参数生成成功:")
                    print(f"    邮箱: {query_params.user_email}")
                    print(f"    日期: {query_params.date}")
                    print(f"    开始时间: {query_params.start_time}")
                    print(f"    结束时间: {query_params.end_time}")
                else:
                    print(f"  ✗ 查询参数生成失败")
            else:
                print(f"  - 非考勤查询，跳过参数生成")
        
        print("\n✓ 意图识别功能测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 意图识别功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ollama_connection():
    """测试Ollama连接"""
    print("\n" + "=" * 50)
    print("测试Ollama连接")
    print("=" * 50)
    
    try:
        from core.utils.ollama_client import ollama_client
        
        # 检查健康状态
        print("检查Ollama服务状态...")
        if ollama_client.check_health():
            print("✓ Ollama服务连接正常")
            
            # 获取模型列表
            models = ollama_client.list_models()
            if models:
                print(f"✓ 可用模型: {models}")
            else:
                print("⚠ 无法获取模型列表")
            
            # 测试简单生成
            print("\n测试AI生成...")
            response = ollama_client.generate("你好，请回复'测试成功'")
            if response:
                print(f"✓ AI响应: {response}")
            else:
                print("⚠ AI生成失败")
            
            return True
        else:
            print("✗ Ollama服务连接失败")
            print("请确保Ollama服务正在运行，并且模型已加载")
            return False
            
    except Exception as e:
        print(f"✗ Ollama连接测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始功能二测试：自然语言意图识别与日期解析")
    print("时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    results = []
    
    # 测试日期解析
    results.append(test_date_parser())
    
    # 测试Ollama连接
    results.append(test_ollama_connection())
    
    # 测试意图识别
    results.append(test_intent_parser())
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！功能二开发完成。")
    else:
        print("⚠ 部分测试失败，请检查相关配置和依赖。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
